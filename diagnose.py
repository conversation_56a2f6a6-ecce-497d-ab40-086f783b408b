#!/usr/bin/env python3
"""
mFAssistant Diagnostic Tool

Helps diagnose why the bot isn't responding.
"""

import sys
import os
import asyncio
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

async def test_bot_connection():
    """Test bot connection to Telegram."""
    try:
        from config.settings import get_settings
        from telegram import Bot
        
        settings = get_settings()
        bot = Bot(token=settings.telegram_bot_token)
        
        print("🔗 Testing bot connection...")
        me = await bot.get_me()
        print(f"✅ Bot connected: @{me.username} ({me.first_name})")
        print(f"✅ Bot ID: {me.id}")
        
        return True, bot
        
    except Exception as e:
        print(f"❌ Bot connection failed: {e}")
        return False, None

async def test_group_access(bot):
    """Test if bot can access the group."""
    try:
        from config.settings import get_settings
        
        settings = get_settings()
        allowed_groups = settings.allowed_groups
        
        print(f"\n📋 Checking group configuration...")
        print(f"✅ Allowed groups: {allowed_groups}")
        
        if not allowed_groups:
            print("⚠️ No groups specified - bot will allow all groups")
            return True
        
        # Try to get chat info for each group
        for group_id in allowed_groups:
            try:
                chat = await bot.get_chat(group_id)
                print(f"✅ Group {group_id}: {chat.title}")
            except Exception as e:
                print(f"❌ Group {group_id}: {e}")
                print(f"   This could mean:")
                print(f"   - Bot is not added to the group")
                print(f"   - Group ID is incorrect")
                print(f"   - Bot was removed from the group")
        
        return True
        
    except Exception as e:
        print(f"❌ Group access test failed: {e}")
        return False

def check_configuration():
    """Check configuration files."""
    print("🔧 Checking configuration...")
    
    # Check .env file
    env_file = Path('.env')
    if not env_file.exists():
        print("❌ .env file not found")
        return False
    
    print("✅ .env file exists")
    
    # Check required variables
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'OPENROUTER_API_KEY',
        'ALLOWED_GROUPS'
    ]
    
    missing_vars = []
    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)
    
    if missing_vars:
        print(f"❌ Missing environment variables: {', '.join(missing_vars)}")
        return False
    
    print("✅ All required environment variables are set")
    
    # Check values
    token = os.getenv('TELEGRAM_BOT_TOKEN', '')
    if not token.count(':') == 1 or len(token) < 20:
        print("❌ TELEGRAM_BOT_TOKEN format looks incorrect")
        print("   Should be like: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz")
        return False
    
    groups = os.getenv('ALLOWED_GROUPS', '')
    if groups and not groups.startswith('-'):
        print("⚠️ ALLOWED_GROUPS should start with '-' for group IDs")
        print(f"   Current value: {groups}")
        print("   Group IDs are usually negative, like: -1001234567890")
    
    return True

async def test_file_processing():
    """Test file processing capabilities."""
    print("\n🎵 Testing file processing capabilities...")
    
    try:
        from services.transcription_service import TranscriptionService
        from config.settings import get_settings
        
        settings = get_settings()
        service = TranscriptionService(settings)
        
        # Test Whisper model info
        info = service.get_model_info()
        print(f"✅ Whisper available: {info['whisper_model_loaded']}")
        print(f"✅ Model size: {info['whisper_model_size']}")
        print(f"✅ Device: {info['device']}")
        print(f"✅ SpeechRecognition fallback: {info['speech_recognition_available']}")
        
        return True
        
    except Exception as e:
        print(f"❌ File processing test failed: {e}")
        return False

async def main():
    """Main diagnostic function."""
    print("=" * 60)
    print("🔍 mFAssistant Diagnostic Tool")
    print("=" * 60)
    
    # Check configuration
    if not check_configuration():
        print("\n❌ Configuration issues found. Please fix and try again.")
        return
    
    # Test bot connection
    connected, bot = await test_bot_connection()
    if not connected:
        print("\n❌ Bot connection failed. Check your TELEGRAM_BOT_TOKEN.")
        return
    
    # Test group access
    await test_group_access(bot)
    
    # Test file processing
    await test_file_processing()
    
    print("\n" + "=" * 60)
    print("🎯 Diagnostic Summary")
    print("=" * 60)
    
    print("\n📋 Next Steps:")
    print("1. If bot connection works but no response in group:")
    print("   - Make sure bot is added to your Telegram group")
    print("   - Check that ALLOWED_GROUPS has the correct group ID")
    print("   - Try sending /start in the group")
    
    print("\n2. To get your group ID:")
    print("   - Add @userinfobot to your group")
    print("   - Forward any message from group to @userinfobot")
    print("   - Copy the group ID (negative number)")
    
    print("\n3. If bot responds to commands but not files:")
    print("   - Upload file as document (not audio message)")
    print("   - Check file is MP3/WAV/M4A format")
    print("   - Ensure file is under 100MB")
    
    print("\n4. Enable debug logging:")
    print("   - Add LOG_LEVEL=DEBUG to your .env file")
    print("   - Restart the bot to see detailed logs")

if __name__ == "__main__":
    asyncio.run(main())
