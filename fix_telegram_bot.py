#!/usr/bin/env python3
"""
Fix Telegram Bot Library Issue

This script fixes the python-telegram-bot compatibility issue.
"""

import subprocess
import sys

def fix_telegram_bot():
    """Fix the telegram bot library issue."""
    print("🔧 Fixing python-telegram-bot compatibility issue...")
    print()
    
    try:
        # Uninstall current version
        print("1. Uninstalling current python-telegram-bot...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'uninstall', 'python-telegram-bot', '-y'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Uninstalled successfully")
        else:
            print("⚠️ Uninstall had issues, continuing...")
        
        # Install specific working version
        print("\n2. Installing compatible version (20.8)...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'python-telegram-bot==20.8'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ python-telegram-bot==20.8 installed successfully")
        else:
            print(f"❌ Installation failed: {result.stderr}")
            return False
        
        # Test import
        print("\n3. Testing import...")
        try:
            import telegram
            from telegram.ext import Application
            print("✅ Import test successful")
            print(f"✅ Version: {telegram.__version__}")
            return True
        except ImportError as e:
            print(f"❌ Import test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Error during fix: {e}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("🎵 mFAssistant - Telegram Bot Fix")
    print("=" * 60)
    print()
    
    if fix_telegram_bot():
        print("\n" + "=" * 60)
        print("🎉 Fix completed successfully!")
        print()
        print("You can now run the bot:")
        print("python main.py")
        print("=" * 60)
    else:
        print("\n" + "=" * 60)
        print("❌ Fix failed. Try manual installation:")
        print()
        print("pip uninstall python-telegram-bot -y")
        print("pip install python-telegram-bot==20.8")
        print("=" * 60)

if __name__ == "__main__":
    main()
