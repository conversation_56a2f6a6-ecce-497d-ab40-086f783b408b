#!/usr/bin/env python3
"""
Python 3.13 Compatibility Fix

This script fixes compatibility issues with Python 3.13 and python-telegram-bot.
"""

import subprocess
import sys
import os

def print_header():
    """Print header."""
    print("=" * 60)
    print("🔧 Python 3.13 Compatibility Fix")
    print("=" * 60)
    print()

def check_python_version():
    """Check Python version."""
    version = sys.version_info
    print(f"📋 Python version: {version.major}.{version.minor}.{version.micro}")
    
    if version.major == 3 and version.minor >= 13:
        print("⚠️ Python 3.13+ detected - applying compatibility fixes")
        return True
    else:
        print("✅ Python version should be compatible")
        return False

def fix_telegram_bot():
    """Fix telegram bot compatibility."""
    print("\n🔧 Fixing python-telegram-bot compatibility...")
    
    try:
        # Try installing a specific version that works better with Python 3.13
        print("1. Installing compatible python-telegram-bot version...")
        
        # Uninstall current version
        subprocess.run([
            sys.executable, '-m', 'pip', 'uninstall', 'python-telegram-bot', '-y'
        ], capture_output=True)
        
        # Install specific version
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'python-telegram-bot==20.7'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ python-telegram-bot 20.7 installed")
        else:
            print("⚠️ Version 20.7 failed, trying 21.0...")
            # Try newer version
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'python-telegram-bot==21.0'
            ], capture_output=True, text=True)
            
            if result.returncode == 0:
                print("✅ python-telegram-bot 21.0 installed")
            else:
                print("❌ Both versions failed")
                return False
        
        # Test import
        try:
            import telegram
            from telegram.ext import Application
            print("✅ Import test successful")
            return True
        except Exception as e:
            print(f"❌ Import test failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Fix failed: {e}")
        return False

def create_compatibility_wrapper():
    """Create a compatibility wrapper."""
    print("\n📝 Creating compatibility wrapper...")
    
    wrapper_content = '''"""
Compatibility wrapper for Python 3.13
"""

import sys
import logging

logger = logging.getLogger(__name__)

def create_telegram_bot(settings):
    """Create telegram bot with compatibility handling."""
    try:
        # Try the simple bot first
        from bot.simple_bot import SimpleTelegramBot
        logger.info("Using simplified bot for Python 3.13 compatibility")
        return SimpleTelegramBot(settings)
    except Exception as e:
        logger.warning(f"Simple bot failed: {e}")
        
        try:
            # Fallback to original bot
            from bot.telegram_handler import TelegramBot
            logger.info("Using original bot implementation")
            return TelegramBot(settings)
        except Exception as e2:
            logger.error(f"Both bot implementations failed: {e2}")
            raise RuntimeError(f"Cannot create telegram bot: {e2}")
'''
    
    try:
        with open('src/bot/compat.py', 'w') as f:
            f.write(wrapper_content)
        print("✅ Compatibility wrapper created")
        return True
    except Exception as e:
        print(f"❌ Failed to create wrapper: {e}")
        return False

def update_main_py():
    """Update main.py to use compatibility wrapper."""
    print("\n🔄 Updating main.py...")
    
    try:
        # Read current main.py
        with open('main.py', 'r') as f:
            content = f.read()
        
        # Replace the import
        old_import = "from bot.telegram_handler import TelegramBot"
        new_import = "from bot.compat import create_telegram_bot"
        
        if old_import in content:
            content = content.replace(old_import, new_import)
            
            # Also update the bot creation
            old_creation = "bot = TelegramBot(settings)"
            new_creation = "bot = create_telegram_bot(settings)"
            content = content.replace(old_creation, new_creation)
            
            # Write back
            with open('main.py', 'w') as f:
                f.write(content)
            
            print("✅ main.py updated")
            return True
        else:
            print("⚠️ main.py already updated or different format")
            return True
            
    except Exception as e:
        print(f"❌ Failed to update main.py: {e}")
        return False

def test_bot():
    """Test bot creation."""
    print("\n🧪 Testing bot creation...")
    
    try:
        # Add src to path
        sys.path.insert(0, 'src')
        
        # Test configuration
        from config.settings import Settings
        print("✅ Settings import OK")
        
        # Test bot creation (this will fail if env vars not set, which is OK)
        try:
            settings = Settings()
            from bot.compat import create_telegram_bot
            bot = create_telegram_bot(settings)
            print("✅ Bot creation test passed")
            return True
        except Exception as e:
            if "Missing required environment variables" in str(e):
                print("✅ Bot creation OK (missing env vars expected)")
                return True
            else:
                print(f"❌ Bot creation failed: {e}")
                return False
                
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main function."""
    print_header()
    
    is_python313 = check_python_version()
    
    success = True
    
    if is_python313:
        success &= fix_telegram_bot()
        success &= create_compatibility_wrapper()
        success &= update_main_py()
    
    success &= test_bot()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 Compatibility fix completed!")
        print()
        print("You can now try running:")
        print("python main.py")
        print()
        print("If you still get errors, check that your .env file is configured:")
        print("- TELEGRAM_BOT_TOKEN")
        print("- OPENROUTER_API_KEY") 
        print("- ALLOWED_GROUPS")
    else:
        print("❌ Fix failed. Try manual installation:")
        print()
        print("pip uninstall python-telegram-bot -y")
        print("pip install python-telegram-bot==20.7")
        print()
        print("Or use Python 3.11 or 3.12 instead of 3.13")
    
    print("=" * 60)

if __name__ == "__main__":
    main()
