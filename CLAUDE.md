# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Plan & Preview

### Before starting work
- Always in plan mode to make a plan
- After get the plan, make sure you Write the plan to docs/PLANNING.MD
- The plan should be a detailed implementation plan and the reasoning behind them, as well as tasks broken down in docs/TASKS.md
- If the task require external knowledge or certain package, also research to get latest knowledge (Use Task tool for research)
- Don't over plan it, always think MVP.
- Once you wrtie the plan, firstly ask me to review it. Do not continue until I approve the plan.

### While implementing
- You should update the plan as you work.
- After you complete tasks in the plan, you should update and append detailed descriptions of the changes you made, so following tasks can be easily hand over to other engineers.

## Project Overview

This is the mFAssistant project - an AI assistant application currently in initial development phase.

## Development Commands

Since this is a new Python project, the following commands will likely be needed as development progresses:

```bash
# Install dependencies (once requirements.txt or pyproject.toml is created)
pip install -r requirements.txt

# Run the application
python main.py

# Run tests 
pytest

# Run tests with coverage
pytest --cov

# Type checking
mypy .

# Code formatting
black .

# Linting
flake8 .
# or
ruff check .
```

## Architecture Notes

This is a new project. Key architectural decisions and patterns will be documented here as the codebase develops.

## Development Setup

1. Ensure Python 3.8+ is installed
2. Create and activate a virtual environment:
   ```bash
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   ```
3. Install dependencies when available
4. Run tests to verify setup: `pytest`