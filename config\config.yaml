# mFAssistant Configuration File

# Audio Processing Settings
audio:
  sample_rate: 16000
  chunk_duration_seconds: 30
  max_file_size_mb: 100
  supported_formats: ["mp3", "wav", "m4a"]
  preprocessing:
    normalize_audio: true
    remove_silence: true
    noise_reduction: false

# Whisper Configuration
whisper:
  model_sizes: ["tiny", "base", "small", "medium", "large"]
  default_model: "base"
  language: "auto"  # auto-detect or specify language code
  temperature: 0.0
  best_of: 5
  beam_size: 5
  patience: 1.0
  length_penalty: 1.0
  suppress_tokens: "-1"
  initial_prompt: ""
  condition_on_previous_text: true
  fp16: true
  compression_ratio_threshold: 2.4
  logprob_threshold: -1.0
  no_speech_threshold: 0.6

# OpenRouter LLM Configuration
llm:
  models:
    primary: "meta-llama/llama-3.3-70b-instruct:free"
    fallback: "mistralai/mistral-small-3.2-24b:free"
    backup: "meta-llama/llama-3.2-11b-vision-instruct:free"
  
  request_settings:
    temperature: 0.7
    max_tokens: 2048
    top_p: 0.9
    frequency_penalty: 0.0
    presence_penalty: 0.0
    
  rate_limits:
    requests_per_day: 200
    requests_per_minute: 10
    retry_attempts: 3
    retry_delay_seconds: 5

# Telegram Bot Settings
telegram:
  file_download_timeout: 300
  message_timeout: 30
  connection_pool_size: 8
  read_timeout: 30
  write_timeout: 30
  connect_timeout: 30
  pool_timeout: 30

# Processing Settings
processing:
  timeout_minutes: 10
  progress_update_interval: 30
  temp_file_cleanup: true
  max_concurrent_jobs: 3
  queue_size: 10

# Markdown Output Settings
markdown:
  template: "default"
  include_timestamps: true
  include_confidence_scores: false
  max_summary_length: 1000
  max_action_items: 20
  
  sections:
    - "metadata"
    - "executive_summary"
    - "key_points"
    - "action_items"
    - "participants"
    - "next_steps"

# Logging Configuration
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  file_rotation: true
  max_file_size_mb: 10
  backup_count: 5
  
  loggers:
    telegram: "INFO"
    whisper: "WARNING"
    llm: "INFO"
    audio: "INFO"
