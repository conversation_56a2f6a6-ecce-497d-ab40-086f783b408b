2025-08-01 00:31:54,614 - root - INFO - mFAssistant starting up...
2025-08-01 00:31:54,614 - root - INFO - Using Whisper model: base
2025-08-01 00:31:54,615 - root - INFO - Primary LLM model: meta-llama/llama-3.3-70b-instruct:free
2025-08-01 00:31:54,615 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:31:55,454 - root - ERROR - Unexpected error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\main.py", line 39, in main
    bot = TelegramBot(settings)
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 36, in __init__
    self._setup_application()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 43, in _setup_application
    ).build()
      ~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_applicationbuilder.py", line 312, in build
    updater = Updater(bot=bot, update_queue=update_queue)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 128, in __init__
    self.__polling_cleanup_cb: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-08-01 00:33:27,006 - root - INFO - mFAssistant starting up...
2025-08-01 00:33:27,007 - root - INFO - Using Whisper model: base
2025-08-01 00:33:27,007 - root - INFO - Primary LLM model: meta-llama/llama-3.3-70b-instruct:free
2025-08-01 00:33:27,007 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:33:27,833 - root - ERROR - Unexpected error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\main.py", line 39, in main
    bot = TelegramBot(settings)
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 36, in __init__
    self._setup_application()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 43, in _setup_application
    ).build()
      ~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_applicationbuilder.py", line 312, in build
    updater = Updater(bot=bot, update_queue=update_queue)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 128, in __init__
    self.__polling_cleanup_cb: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-08-01 00:37:45,419 - bot.compat - INFO - Using simplified bot for Python 3.13 compatibility
2025-08-01 00:37:45,420 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:38:09,265 - root - INFO - mFAssistant starting up...
2025-08-01 00:38:09,266 - root - INFO - Using Whisper model: base
2025-08-01 00:38:09,266 - root - INFO - Primary LLM model: meta-llama/llama-3.3-70b-instruct:free
2025-08-01 00:38:13,205 - bot.compat - INFO - Using simplified bot for Python 3.13 compatibility
2025-08-01 00:38:13,206 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:38:13,206 - root - INFO - Starting Telegram bot...
2025-08-01 00:38:14,070 - bot.simple_bot - ERROR - Bot setup failed: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-08-01 00:38:14,072 - root - ERROR - Unexpected error: Failed to setup bot
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\main.py", line 43, in main
    await bot.start()
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\simple_bot.py", line 198, in start
    raise RuntimeError("Failed to setup bot")
RuntimeError: Failed to setup bot
