"""
Markdown Generator Service

Generates formatted markdown reports from meeting analysis results.
"""

import logging
from datetime import datetime
from typing import Dict, Any, List, Optional
from pathlib import Path

from config.settings import Settings

logger = logging.getLogger(__name__)

class MarkdownGenerator:
    """Generates markdown reports from meeting analysis."""
    
    def __init__(self, settings: Settings):
        """Initialize the markdown generator."""
        self.settings = settings
    
    def generate_meeting_report(
        self, 
        analysis: Dict[str, Any], 
        transcription_metadata: Dict[str, Any],
        transcription_text: str
    ) -> str:
        """
        Generate a complete meeting report in markdown format.
        
        Args:
            analysis: LLM analysis results
            transcription_metadata: Audio file metadata
            transcription_text: Original transcription text
            
        Returns:
            Formatted markdown report
        """
        try:
            # Get configuration
            config = self.settings._config_data.get("markdown", {})
            include_timestamps = config.get("include_timestamps", True)
            include_confidence = config.get("include_confidence_scores", False)
            
            # Build report sections
            sections = []
            
            # Header
            sections.append(self._generate_header(transcription_metadata))
            
            # Metadata section
            if "metadata" in config.get("sections", []):
                sections.append(self._generate_metadata_section(
                    transcription_metadata, analysis, include_confidence
                ))
            
            # Executive summary
            if "executive_summary" in config.get("sections", []):
                sections.append(self._generate_executive_summary(analysis))
            
            # Key points
            if "key_points" in config.get("sections", []):
                sections.append(self._generate_key_points(analysis))
            
            # Action items
            if "action_items" in config.get("sections", []):
                sections.append(self._generate_action_items(analysis))
            
            # Participants
            if "participants" in config.get("sections", []):
                sections.append(self._generate_participants(analysis))
            
            # Decisions made
            sections.append(self._generate_decisions(analysis))
            
            # Next steps
            if "next_steps" in config.get("sections", []):
                sections.append(self._generate_next_steps(analysis))
            
            # Topics discussed
            sections.append(self._generate_topics(analysis))
            
            # Full transcription (optional)
            if include_timestamps and transcription_text:
                sections.append(self._generate_transcription_section(
                    transcription_text, transcription_metadata
                ))
            
            # Footer
            sections.append(self._generate_footer(analysis))
            
            # Combine all sections
            report = "\n\n".join(filter(None, sections))
            
            logger.info("Meeting report generated successfully")
            return report
            
        except Exception as e:
            logger.error(f"Failed to generate markdown report: {e}")
            return self._generate_error_report(str(e), transcription_metadata)
    
    def _generate_header(self, metadata: Dict[str, Any]) -> str:
        """Generate report header."""
        filename = metadata.get("original_filename", "Unknown")
        date = datetime.now().strftime("%B %d, %Y")
        time = datetime.now().strftime("%I:%M %p")
        
        return f"""# Meeting Report: {filename}

**Generated:** {date} at {time}  
**Processed by:** mFAssistant (Cost-Free AI Meeting Assistant)

---"""
    
    def _generate_metadata_section(
        self, 
        metadata: Dict[str, Any], 
        analysis: Dict[str, Any],
        include_confidence: bool
    ) -> str:
        """Generate metadata section."""
        duration = metadata.get("duration_minutes", "Unknown")
        file_size = metadata.get("file_size_mb", "Unknown")
        format_info = metadata.get("format", "Unknown")
        
        # Get processing info
        analysis_meta = analysis.get("metadata", {})
        model_used = analysis_meta.get("model_used", "Unknown")
        
        section = f"""## 📊 Meeting Information

| **Attribute** | **Value** |
|---------------|-----------|
| **Duration** | {duration} minutes |
| **File Size** | {file_size} MB |
| **Format** | {format_info} |
| **AI Model** | {model_used.split('/')[-1] if '/' in str(model_used) else model_used} |
| **Processing** | Local Whisper + OpenRouter Free Tier |"""
        
        if include_confidence and "confidence" in metadata:
            confidence = metadata.get("confidence", 0)
            section += f"\n| **Confidence** | {confidence:.1%} |"
        
        return section
    
    def _generate_executive_summary(self, analysis: Dict[str, Any]) -> str:
        """Generate executive summary section."""
        summary = analysis.get("executive_summary", "No summary available.")
        
        return f"""## 📋 Executive Summary

{summary}"""
    
    def _generate_key_points(self, analysis: Dict[str, Any]) -> str:
        """Generate key points section."""
        key_points = analysis.get("key_points", [])
        
        if not key_points:
            return "## 🔑 Key Points\n\nNo key points identified."
        
        points_list = "\n".join([f"- {point}" for point in key_points])
        
        return f"""## 🔑 Key Points

{points_list}"""
    
    def _generate_action_items(self, analysis: Dict[str, Any]) -> str:
        """Generate action items section."""
        action_items = analysis.get("action_items", [])
        
        if not action_items:
            return "## ✅ Action Items\n\n*No action items identified.*"
        
        # Format action items
        items_md = []
        for i, item in enumerate(action_items, 1):
            if isinstance(item, dict):
                task = item.get("task", "Unknown task")
                assignee = item.get("assignee", "Unassigned")
                deadline = item.get("deadline", "No deadline")
                priority = item.get("priority", "medium")
                
                priority_emoji = {"high": "🔴", "medium": "🟡", "low": "🟢"}.get(priority, "⚪")
                
                items_md.append(f"""### {i}. {task}
- **Assignee:** {assignee}
- **Deadline:** {deadline}
- **Priority:** {priority_emoji} {priority.title()}""")
            else:
                items_md.append(f"{i}. {item}")
        
        items_text = "\n\n".join(items_md)
        
        return f"""## ✅ Action Items

{items_text}"""
    
    def _generate_participants(self, analysis: Dict[str, Any]) -> str:
        """Generate participants section."""
        participants = analysis.get("participants", [])
        
        if not participants:
            return "## 👥 Participants\n\n*No participants identified.*"
        
        participants_list = "\n".join([f"- {participant}" for participant in participants])
        
        return f"""## 👥 Participants

{participants_list}"""
    
    def _generate_decisions(self, analysis: Dict[str, Any]) -> str:
        """Generate decisions made section."""
        decisions = analysis.get("decisions_made", [])
        
        if not decisions:
            return "## 🎯 Decisions Made\n\n*No specific decisions identified.*"
        
        decisions_list = "\n".join([f"- {decision}" for decision in decisions])
        
        return f"""## 🎯 Decisions Made

{decisions_list}"""
    
    def _generate_next_steps(self, analysis: Dict[str, Any]) -> str:
        """Generate next steps section."""
        next_steps = analysis.get("next_steps", [])
        
        if not next_steps:
            return "## 🚀 Next Steps\n\n*No next steps identified.*"
        
        steps_list = "\n".join([f"- {step}" for step in next_steps])
        
        return f"""## 🚀 Next Steps

{steps_list}"""
    
    def _generate_topics(self, analysis: Dict[str, Any]) -> str:
        """Generate topics discussed section."""
        topics = analysis.get("topics_discussed", [])
        
        if not topics:
            return "## 💬 Topics Discussed\n\n*No specific topics identified.*"
        
        topics_list = "\n".join([f"- {topic}" for topic in topics])
        
        return f"""## 💬 Topics Discussed

{topics_list}"""
    
    def _generate_transcription_section(
        self, 
        transcription: str, 
        metadata: Dict[str, Any]
    ) -> str:
        """Generate full transcription section."""
        method = metadata.get("method", "Unknown")
        
        # Truncate if too long
        max_length = 5000
        if len(transcription) > max_length:
            transcription = transcription[:max_length] + "\n\n*[Transcription truncated for brevity]*"
        
        return f"""## 📝 Full Transcription

*Transcribed using: {method.title()}*

{transcription}"""
    
    def _generate_footer(self, analysis: Dict[str, Any]) -> str:
        """Generate report footer."""
        analysis_meta = analysis.get("metadata", {})
        generated_at = analysis_meta.get("generated_at", datetime.now().isoformat())
        
        # Parse timestamp for better formatting
        try:
            dt = datetime.fromisoformat(generated_at.replace('Z', '+00:00'))
            formatted_time = dt.strftime("%B %d, %Y at %I:%M %p")
        except:
            formatted_time = generated_at
        
        return f"""---

## 🤖 Processing Information

**Generated by:** mFAssistant - Cost-Free AI Meeting Assistant  
**Analysis completed:** {formatted_time}  
**Technology:** Local Whisper + OpenRouter Free Tier Models  

*This report was generated automatically. Please review for accuracy.*"""
    
    def _generate_error_report(self, error: str, metadata: Dict[str, Any]) -> str:
        """Generate error report when processing fails."""
        filename = metadata.get("original_filename", "Unknown")
        
        return f"""# Meeting Report: {filename}

## ❌ Processing Error

An error occurred while generating the meeting report:

```
{error}
```

## 📊 File Information

- **Filename:** {filename}
- **Duration:** {metadata.get('duration_minutes', 'Unknown')} minutes
- **File Size:** {metadata.get('file_size_mb', 'Unknown')} MB
- **Generated:** {datetime.now().strftime('%B %d, %Y at %I:%M %p')}

Please try processing the file again or contact support if the issue persists.

---

*Generated by mFAssistant*"""
    
    def generate_filename(self, metadata: Dict[str, Any]) -> str:
        """Generate appropriate filename for the markdown report."""
        original_name = metadata.get("original_filename", "meeting")
        
        # Remove extension and clean up
        base_name = Path(original_name).stem
        base_name = "".join(c for c in base_name if c.isalnum() or c in (' ', '-', '_')).strip()
        
        # Add timestamp
        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        # Create filename
        filename = f"meeting_report_{base_name}_{timestamp}.md"
        
        # Ensure reasonable length
        if len(filename) > 100:
            filename = f"meeting_report_{timestamp}.md"
        
        return filename
