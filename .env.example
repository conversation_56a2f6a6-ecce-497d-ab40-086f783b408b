# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
ALLOWED_GROUPS=group_id_1,group_id_2

# OpenRouter Free Tier Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PRIMARY_LLM_MODEL=meta-llama/llama-3.3-70b-instruct:free
FALLBACK_LLM_MODEL=mistralai/mistral-small-3.2-24b:free

# Whisper Configuration
WHISPER_MODEL_SIZE=base
USE_GPU=false
WHISPER_LANGUAGE=auto

# Processing Configuration
MAX_FILE_SIZE_MB=100
PROCESSING_TIMEOUT_MINUTES=10
TEMP_DIR=./temp

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/mfassistant.log
