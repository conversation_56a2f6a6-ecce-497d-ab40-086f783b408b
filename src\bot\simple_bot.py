"""
Simple Telegram Bo<PERSON> Handler

A simplified version that should work better with Python 3.13.
"""

import logging
import asyncio
from typing import Optional

from telegram import Update
from telegram.ext import Application, CommandHandler, MessageHandler, filters, ContextTypes
from telegram.error import TelegramError

from config.settings import Settings
from bot.message_processor import MessageProcessor

logger = logging.getLogger(__name__)

class SimpleTelegramBot:
    """Simplified Telegram bot for better compatibility."""
    
    def __init__(self, settings: Settings):
        """Initialize the bot."""
        self.settings = settings
        self.application: Optional[Application] = None
        self.message_processor = MessageProcessor(settings)
        self._running = False
    
    async def setup(self) -> bool:
        """Setup the bot application."""
        try:
            # Create application with minimal configuration
            self.application = (
                Application.builder()
                .token(self.settings.telegram_bot_token)
                .build()
            )
            
            # Add handlers
            self.application.add_handler(CommandHandler("start", self.start_command))
            self.application.add_handler(<PERSON>Handler("help", self.help_command))
            self.application.add_handler(CommandHandler("status", self.status_command))
            
            # File handlers
            self.application.add_handler(
                MessageHandler(filters.Document.ALL, self.handle_document)
            )
            self.application.add_handler(
                MessageHandler(filters.AUDIO, self.handle_audio)
            )
            
            # Error handler
            self.application.add_error_handler(self.error_handler)
            
            logger.info("Bot setup completed successfully")
            return True
            
        except Exception as e:
            logger.error(f"Bot setup failed: {e}")
            return False
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command."""
        if not self._is_authorized_group(update):
            await update.message.reply_text("❌ This bot is not authorized for this group.")
            return
        
        message = (
            "🎵 **mFAssistant - Meeting File Assistant**\n\n"
            "Upload an MP3 file and I'll create a meeting summary!\n\n"
            "**Commands:**\n"
            "• /help - Show help\n"
            "• /status - Check status\n\n"
            "Ready to process your files! 🚀"
        )
        
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /help command."""
        if not self._is_authorized_group(update):
            return
        
        message = (
            "🔧 **mFAssistant Help**\n\n"
            "**How to use:**\n"
            "1. Upload an MP3, WAV, or M4A file\n"
            "2. Wait for processing (may take a few minutes)\n"
            "3. Receive your meeting summary!\n\n"
            f"**Max file size:** {self.settings.max_file_size_mb}MB\n"
            "**Features:** Cost-free transcription + AI analysis"
        )
        
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /status command."""
        if not self._is_authorized_group(update):
            return
        
        message = (
            "📊 **Bot Status**\n\n"
            f"🤖 **Status:** Online ✅\n"
            f"🎵 **Whisper:** {self.settings.whisper_model_size}\n"
            f"🧠 **LLM:** {self.settings.primary_llm_model.split('/')[-1]}\n"
            f"📁 **Max Size:** {self.settings.max_file_size_mb}MB\n\n"
            "Ready to process files! 🚀"
        )
        
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def handle_document(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle document uploads."""
        if not self._is_authorized_group(update):
            return
        
        document = update.message.document
        if not document:
            return
        
        # Check if it's an audio file
        if document.mime_type and document.mime_type.startswith('audio/'):
            await self._process_file(update, context, document)
        else:
            await update.message.reply_text("❌ Please upload an audio file (MP3, WAV, M4A).")
    
    async def handle_audio(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle audio uploads."""
        if not self._is_authorized_group(update):
            return
        
        audio = update.message.audio
        if audio:
            await self._process_file(update, context, audio)
    
    async def _process_file(self, update: Update, context: ContextTypes.DEFAULT_TYPE, file_obj) -> None:
        """Process uploaded file."""
        try:
            # Check file size
            file_size_mb = file_obj.file_size / (1024 * 1024) if file_obj.file_size else 0
            
            if not self.settings.validate_file_size(file_size_mb):
                await update.message.reply_text(
                    f"❌ File too large! Max size: {self.settings.max_file_size_mb}MB"
                )
                return
            
            # Send processing message
            processing_msg = await update.message.reply_text(
                "🎵 **Processing your audio file...**\n\n"
                "📥 Starting processing...\n"
                "⏳ This may take a few minutes.\n\n"
                "I'll update you on progress!",
                parse_mode='Markdown'
            )
            
            # Process the file
            await self.message_processor.process_audio_file(
                update, context, file_obj, processing_msg
            )
            
        except Exception as e:
            logger.error(f"File processing error: {e}")
            await update.message.reply_text(
                "❌ Sorry, there was an error processing your file. Please try again."
            )
    
    def _is_authorized_group(self, update: Update) -> bool:
        """Check if message is from authorized group."""
        if not update.message or not update.message.chat:
            return False
        
        chat_id = str(update.message.chat.id)
        allowed_groups = self.settings.allowed_groups
        
        if not allowed_groups:
            logger.warning("No allowed groups specified - allowing all")
            return True
        
        return chat_id in allowed_groups
    
    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors."""
        logger.error(f"Update {update} caused error {context.error}")
        
        if isinstance(update, Update) and update.message:
            try:
                await update.message.reply_text(
                    "❌ An error occurred. Please try again or contact support."
                )
            except TelegramError:
                pass
    
    async def start(self) -> None:
        """Start the bot."""
        if not await self.setup():
            raise RuntimeError("Failed to setup bot")
        
        if not self.application:
            raise RuntimeError("Application not initialized")
        
        try:
            logger.info("Starting Telegram bot...")
            self._running = True
            
            # Initialize and start
            await self.application.initialize()
            await self.application.start()
            await self.application.updater.start_polling()
            
            logger.info("Bot is running. Press Ctrl+C to stop.")
            
            # Keep running
            while self._running:
                await asyncio.sleep(1)
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await self.stop()
    
    async def stop(self) -> None:
        """Stop the bot."""
        self._running = False
        
        if self.application:
            logger.info("Stopping bot...")
            try:
                await self.application.updater.stop()
                await self.application.stop()
                await self.application.shutdown()
                logger.info("Bot stopped successfully")
            except Exception as e:
                logger.error(f"Error stopping bot: {e}")

# Alias for compatibility
TelegramBot = SimpleTelegramBot
