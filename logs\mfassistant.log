2025-08-01 00:31:54,614 - root - INFO - mFAssistant starting up...
2025-08-01 00:31:54,614 - root - INFO - Using Whisper model: base
2025-08-01 00:31:54,615 - root - INFO - Primary LLM model: meta-llama/llama-3.3-70b-instruct:free
2025-08-01 00:31:54,615 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:31:55,454 - root - ERROR - Unexpected error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\main.py", line 39, in main
    bot = TelegramBot(settings)
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 36, in __init__
    self._setup_application()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 43, in _setup_application
    ).build()
      ~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_applicationbuilder.py", line 312, in build
    updater = Updater(bot=bot, update_queue=update_queue)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 128, in __init__
    self.__polling_cleanup_cb: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
2025-08-01 00:33:27,006 - root - INFO - mFAssistant starting up...
2025-08-01 00:33:27,007 - root - INFO - Using Whisper model: base
2025-08-01 00:33:27,007 - root - INFO - Primary LLM model: meta-llama/llama-3.3-70b-instruct:free
2025-08-01 00:33:27,007 - services.transcription_service - INFO - SpeechRecognition fallback initialized
2025-08-01 00:33:27,833 - root - ERROR - Unexpected error: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
Traceback (most recent call last):
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\main.py", line 39, in main
    bot = TelegramBot(settings)
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 36, in __init__
    self._setup_application()
    ~~~~~~~~~~~~~~~~~~~~~~~^^
  File "C:\Users\<USER>\Documents\Python Scripts\elvison-ai\mFAssistant\src\bot\telegram_handler.py", line 43, in _setup_application
    ).build()
      ~~~~~^^
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_applicationbuilder.py", line 312, in build
    updater = Updater(bot=bot, update_queue=update_queue)
  File "C:\Users\<USER>\AppData\Roaming\Python\Python313\site-packages\telegram\ext\_updater.py", line 128, in __init__
    self.__polling_cleanup_cb: Optional[Callable[[], Coroutine[Any, Any, None]]] = None
    ^^^^^^^^^^^^^^^^^^^^^^^^^
AttributeError: 'Updater' object has no attribute '_Updater__polling_cleanup_cb' and no __dict__ for setting new attributes
