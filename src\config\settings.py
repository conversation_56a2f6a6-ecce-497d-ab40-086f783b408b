"""
Configuration Management System

Handles loading and validation of environment variables and configuration files.
"""

import os
import logging
import yaml
from pathlib import Path
from typing import Dict, List, Optional, Any
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class ConfigurationError(Exception):
    """Raised when configuration is invalid or missing."""
    pass

class Settings:
    """Application settings manager."""
    
    def __init__(self, config_file: Optional[str] = None):
        """Initialize settings with optional config file path."""
        self.config_file = config_file or "config/config.yaml"
        self._config_data = {}
        self._load_config()
        self._validate_required_settings()
        self._setup_logging()
    
    def _load_config(self) -> None:
        """Load configuration from YAML file."""
        config_path = Path(self.config_file)
        if config_path.exists():
            try:
                with open(config_path, 'r', encoding='utf-8') as f:
                    self._config_data = yaml.safe_load(f) or {}
            except yaml.YAMLError as e:
                raise ConfigurationError(f"Invalid YAML in config file: {e}")
            except Exception as e:
                raise ConfigurationError(f"Error reading config file: {e}")
        else:
            logging.warning(f"Config file {config_path} not found, using defaults")
    
    def _validate_required_settings(self) -> None:
        """Validate that all required environment variables are set."""
        required_vars = [
            "TELEGRAM_BOT_TOKEN",
            "OPENROUTER_API_KEY",
            "ALLOWED_GROUPS"
        ]
        
        missing_vars = []
        for var in required_vars:
            if not os.getenv(var):
                missing_vars.append(var)
        
        if missing_vars:
            raise ConfigurationError(
                f"Missing required environment variables: {', '.join(missing_vars)}"
            )
    
    def _setup_logging(self) -> None:
        """Setup logging configuration."""
        log_level = getattr(logging, self.log_level.upper(), logging.INFO)
        log_format = self.get_config("logging.format", 
                                   "%(asctime)s - %(name)s - %(levelname)s - %(message)s")
        
        # Create logs directory if it doesn't exist
        log_dir = Path("logs")
        log_dir.mkdir(exist_ok=True)
        
        # Configure logging
        logging.basicConfig(
            level=log_level,
            format=log_format,
            handlers=[
                logging.FileHandler(self.log_file),
                logging.StreamHandler()
            ]
        )
    
    def get_config(self, key: str, default: Any = None) -> Any:
        """Get configuration value using dot notation (e.g., 'audio.sample_rate')."""
        keys = key.split('.')
        value = self._config_data
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    # Telegram Bot Settings
    @property
    def telegram_bot_token(self) -> str:
        """Telegram bot token."""
        return os.getenv("TELEGRAM_BOT_TOKEN", "")
    
    @property
    def allowed_groups(self) -> List[str]:
        """List of allowed Telegram group IDs."""
        groups = os.getenv("ALLOWED_GROUPS", "")
        return [g.strip() for g in groups.split(",") if g.strip()]
    
    # OpenRouter LLM Settings
    @property
    def openrouter_api_key(self) -> str:
        """OpenRouter API key."""
        return os.getenv("OPENROUTER_API_KEY", "")
    
    @property
    def openrouter_api_base(self) -> str:
        """OpenRouter API base URL."""
        return os.getenv("OPENROUTER_API_BASE", "https://openrouter.ai/api/v1")
    
    @property
    def primary_llm_model(self) -> str:
        """Primary LLM model for analysis."""
        return os.getenv("PRIMARY_LLM_MODEL", "meta-llama/llama-3.3-70b-instruct:free")
    
    @property
    def fallback_llm_model(self) -> str:
        """Fallback LLM model."""
        return os.getenv("FALLBACK_LLM_MODEL", "mistralai/mistral-small-3.2-24b:free")
    
    # Whisper Settings
    @property
    def whisper_model_size(self) -> str:
        """Whisper model size."""
        return os.getenv("WHISPER_MODEL_SIZE", "base")
    
    @property
    def use_gpu(self) -> bool:
        """Whether to use GPU for Whisper."""
        return os.getenv("USE_GPU", "false").lower() == "true"
    
    @property
    def whisper_language(self) -> str:
        """Whisper language setting."""
        return os.getenv("WHISPER_LANGUAGE", "auto")
    
    # Processing Settings
    @property
    def max_file_size_mb(self) -> int:
        """Maximum file size in MB."""
        return int(os.getenv("MAX_FILE_SIZE_MB", "100"))
    
    @property
    def processing_timeout_minutes(self) -> int:
        """Processing timeout in minutes."""
        return int(os.getenv("PROCESSING_TIMEOUT_MINUTES", "10"))
    
    @property
    def temp_dir(self) -> str:
        """Temporary directory for file processing."""
        return os.getenv("TEMP_DIR", "./temp")
    
    # Logging Settings
    @property
    def log_level(self) -> str:
        """Logging level."""
        return os.getenv("LOG_LEVEL", "INFO")
    
    @property
    def log_file(self) -> str:
        """Log file path."""
        return os.getenv("LOG_FILE", "./logs/mfassistant.log")
    
    # Audio Processing Settings
    @property
    def audio_sample_rate(self) -> int:
        """Audio sample rate for processing."""
        return self.get_config("audio.sample_rate", 16000)
    
    @property
    def audio_chunk_duration(self) -> int:
        """Audio chunk duration in seconds."""
        return self.get_config("audio.chunk_duration_seconds", 30)
    
    # LLM Request Settings
    @property
    def llm_temperature(self) -> float:
        """LLM temperature setting."""
        return self.get_config("llm.request_settings.temperature", 0.7)
    
    @property
    def llm_max_tokens(self) -> int:
        """LLM max tokens setting."""
        return self.get_config("llm.request_settings.max_tokens", 2048)
    
    @property
    def llm_requests_per_day(self) -> int:
        """LLM requests per day limit."""
        return self.get_config("llm.rate_limits.requests_per_day", 200)
    
    def validate_whisper_model(self) -> bool:
        """Validate that the specified Whisper model size is supported."""
        valid_sizes = ["tiny", "base", "small", "medium", "large"]
        return self.whisper_model_size in valid_sizes
    
    def validate_file_size(self, size_mb: float) -> bool:
        """Validate that file size is within limits."""
        return size_mb <= self.max_file_size_mb
    
    def get_temp_path(self, filename: str) -> Path:
        """Get full path for temporary file."""
        temp_dir = Path(self.temp_dir)
        temp_dir.mkdir(exist_ok=True)
        return temp_dir / filename

# Global settings instance
settings = Settings()

def get_settings() -> Settings:
    """Get the global settings instance."""
    return settings
