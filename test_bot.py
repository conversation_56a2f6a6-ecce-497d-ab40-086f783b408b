#!/usr/bin/env python3
"""
Test Bot Creation

Quick test to verify bot creation works.
"""

import sys
import logging
from pathlib import Path

# Add src to path
sys.path.insert(0, str(Path(__file__).parent / "src"))

# Setup logging
logging.basicConfig(level=logging.INFO)

def test_bot_creation():
    """Test bot creation."""
    try:
        print("🧪 Testing bot creation...")
        
        # Test configuration
        from config.settings import get_settings, ConfigurationError
        
        try:
            settings = get_settings()
            print("✅ Settings loaded successfully")
        except ConfigurationError as e:
            print(f"⚠️ Configuration issue (expected): {e}")
            print("   This is normal if .env is not configured")
            return True
        
        # Test bot creation
        from bot.compat import create_telegram_bot
        
        try:
            bot = create_telegram_bot(settings)
            print("✅ Bot created successfully")
            print(f"✅ Bot type: {type(bot).__name__}")
            return True
        except Exception as e:
            print(f"❌ Bot creation failed: {e}")
            return False
            
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False

def main():
    """Main function."""
    print("=" * 50)
    print("🎵 mFAssistant Bot Test")
    print("=" * 50)
    
    if test_bot_creation():
        print("\n🎉 Test passed! Bot should work now.")
        print("\nNext steps:")
        print("1. Configure your .env file")
        print("2. Run: python main.py")
    else:
        print("\n❌ Test failed. Check the error messages above.")
    
    print("=" * 50)

if __name__ == "__main__":
    main()
