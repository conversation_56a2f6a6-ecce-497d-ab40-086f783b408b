"""
Message Processor

Handles the complete processing pipeline from audio file to markdown report.
"""

import logging
import asyncio
from pathlib import Path
from typing import Dict, Any, Optional, Callable
from datetime import datetime

from telegram import Update, InputFile
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from config.settings import Settings
from utils.file_handler import AudioFileHandler
from services.transcription_service import TranscriptionService
from services.llm_service import LLMService
from services.markdown_generator import MarkdownGenerator

logger = logging.getLogger(__name__)

class ProcessingError(Exception):
    """Raised when processing pipeline fails."""
    pass

class MessageProcessor:
    """Handles the complete audio processing pipeline."""
    
    def __init__(self, settings: Settings):
        """Initialize the message processor."""
        self.settings = settings
        self.file_handler = AudioFileHandler(settings)
        self.transcription_service = TranscriptionService(settings)
        self.markdown_generator = MarkdownGenerator(settings)
    
    async def process_audio_file(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        file_obj,
        processing_message
    ) -> None:
        """
        Process an audio file through the complete pipeline.
        
        Args:
            update: Telegram update object
            context: Telegram context
            file_obj: Telegram file object
            processing_message: Message to update with progress
        """
        temp_files = []
        
        try:
            # Progress callback for user updates
            async def progress_callback(message: str):
                try:
                    await processing_message.edit_text(
                        f"🎵 **Processing your audio file...**\n\n{message}",
                        parse_mode='Markdown'
                    )
                except TelegramError as e:
                    logger.warning(f"Failed to update progress message: {e}")
            
            # Step 1: Download and validate file
            await progress_callback("📥 Downloading and validating file...")
            
            telegram_file = await file_obj.get_file()
            file_path, metadata = await self.file_handler.download_and_validate_file(
                telegram_file, file_obj.file_name or "audio_file.mp3"
            )
            temp_files.append(file_path)
            
            logger.info(f"File downloaded: {file_path}, duration: {metadata.get('duration_minutes', 0):.1f}min")
            
            # Step 2: Preprocess audio
            await progress_callback("🔧 Preprocessing audio for optimal transcription...")
            
            preprocessed_path = await self.file_handler.preprocess_audio(file_path)
            if preprocessed_path != file_path:
                temp_files.append(preprocessed_path)
            
            # Step 3: Transcribe audio
            await progress_callback("🎯 Starting transcription (this may take a few minutes)...")
            
            transcription_result = await self.transcription_service.transcribe_audio(
                preprocessed_path, metadata, progress_callback
            )
            
            if not transcription_result or not transcription_result.get("text"):
                raise ProcessingError("Transcription failed or returned empty result")
            
            logger.info(f"Transcription completed: {len(transcription_result['text'])} characters")
            
            # Step 4: LLM Analysis
            await progress_callback("🧠 Analyzing meeting content with AI...")
            
            async with LLMService(self.settings) as llm_service:
                analysis_result = await llm_service.analyze_meeting(
                    transcription_result["text"], metadata, progress_callback
                )
            
            logger.info("LLM analysis completed successfully")
            
            # Step 5: Generate markdown report
            await progress_callback("📝 Generating meeting report...")
            
            markdown_content = self.markdown_generator.generate_meeting_report(
                analysis_result, metadata, transcription_result["text"]
            )
            
            # Step 6: Save and upload markdown file
            await progress_callback("📤 Preparing report for upload...")
            
            report_filename = self.markdown_generator.generate_filename(metadata)
            report_path = self.settings.get_temp_path(report_filename)
            temp_files.append(report_path)
            
            # Write markdown file
            with open(report_path, 'w', encoding='utf-8') as f:
                f.write(markdown_content)
            
            # Step 7: Upload to Telegram
            await progress_callback("🚀 Uploading your meeting report...")
            
            await self._upload_report_to_telegram(
                update, context, report_path, metadata, analysis_result
            )
            
            # Success message
            await processing_message.edit_text(
                "✅ **Processing Complete!**\n\n"
                f"📊 **Summary:**\n"
                f"• Duration: {metadata.get('duration_minutes', 0):.1f} minutes\n"
                f"• Transcription: {len(transcription_result['text'])} characters\n"
                f"• Method: {transcription_result.get('method', 'unknown').title()}\n"
                f"• AI Model: {analysis_result.get('metadata', {}).get('model_used', 'unknown').split('/')[-1]}\n\n"
                f"📝 Your meeting report has been uploaded above!",
                parse_mode='Markdown'
            )
            
            logger.info(f"Processing completed successfully for file: {file_obj.file_name}")
            
        except Exception as e:
            logger.error(f"Processing failed: {e}", exc_info=True)
            
            # Send error message
            try:
                error_message = (
                    "❌ **Processing Failed**\n\n"
                    f"Sorry, there was an error processing your audio file:\n"
                    f"`{str(e)}`\n\n"
                    "**Possible solutions:**\n"
                    "• Try a shorter audio file\n"
                    "• Ensure good audio quality\n"
                    "• Check if the file format is supported\n"
                    "• Try again in a few minutes\n\n"
                    "If the problem persists, please contact the administrator."
                )
                
                await processing_message.edit_text(error_message, parse_mode='Markdown')
                
            except TelegramError:
                # If we can't edit the message, send a new one
                try:
                    await update.message.reply_text(
                        "❌ Processing failed. Please try again or contact support.",
                        parse_mode='Markdown'
                    )
                except TelegramError:
                    pass  # Give up on error reporting
        
        finally:
            # Clean up temporary files
            await self._cleanup_temp_files(temp_files)
    
    async def _upload_report_to_telegram(
        self, 
        update: Update, 
        context: ContextTypes.DEFAULT_TYPE,
        report_path: Path,
        metadata: Dict[str, Any],
        analysis: Dict[str, Any]
    ) -> None:
        """Upload the generated report to Telegram."""
        try:
            # Prepare caption
            duration = metadata.get('duration_minutes', 0)
            word_count = analysis.get('metadata', {}).get('token_usage', {}).get('total_tokens', 'unknown')
            model = analysis.get('metadata', {}).get('model_used', 'unknown').split('/')[-1]
            
            caption = (
                f"📋 **Meeting Report Generated**\n\n"
                f"🎵 **Original:** {metadata.get('original_filename', 'Unknown')}\n"
                f"⏱️ **Duration:** {duration:.1f} minutes\n"
                f"🤖 **AI Model:** {model}\n"
                f"📊 **Processing:** Cost-free local + cloud analysis\n\n"
                f"*Generated by mFAssistant*"
            )
            
            # Upload file
            with open(report_path, 'rb') as f:
                await update.message.reply_document(
                    document=InputFile(f, filename=report_path.name),
                    caption=caption,
                    parse_mode='Markdown'
                )
            
            logger.info(f"Report uploaded successfully: {report_path.name}")
            
        except TelegramError as e:
            logger.error(f"Failed to upload report: {e}")
            
            # Try to send a text summary instead
            try:
                summary = analysis.get('executive_summary', 'Summary not available')
                fallback_message = (
                    f"📋 **Meeting Summary** (File upload failed)\n\n"
                    f"**Summary:** {summary}\n\n"
                    f"*Full report generation failed. Please try again.*"
                )
                
                await update.message.reply_text(fallback_message, parse_mode='Markdown')
                
            except TelegramError:
                logger.error("Failed to send fallback summary")
                raise ProcessingError("Failed to deliver results to user")
    
    async def _cleanup_temp_files(self, file_paths: list) -> None:
        """Clean up temporary files."""
        for file_path in file_paths:
            try:
                if isinstance(file_path, Path) and file_path.exists():
                    self.file_handler.cleanup_file(file_path)
            except Exception as e:
                logger.warning(f"Failed to cleanup file {file_path}: {e}")
        
        # Also run general cleanup
        try:
            self.file_handler.cleanup_temp_files(max_age_hours=1)
        except Exception as e:
            logger.warning(f"Failed to run general cleanup: {e}")
    
    async def get_processing_status(self) -> Dict[str, Any]:
        """Get current processing status and capabilities."""
        try:
            # Get model info
            transcription_info = self.transcription_service.get_model_info()
            
            # Get LLM service status
            async with LLMService(self.settings) as llm_service:
                llm_status = llm_service.get_service_status()
            
            return {
                "transcription": transcription_info,
                "llm": llm_status,
                "file_handler": {
                    "max_file_size_mb": self.settings.max_file_size_mb,
                    "supported_formats": ["mp3", "wav", "m4a"],
                    "temp_dir": str(self.settings.temp_dir)
                },
                "processing": {
                    "timeout_minutes": self.settings.processing_timeout_minutes,
                    "chunk_duration": self.settings.audio_chunk_duration
                }
            }
            
        except Exception as e:
            logger.error(f"Failed to get processing status: {e}")
            return {"error": str(e)}
    
    async def estimate_processing_time(self, duration_minutes: float) -> Dict[str, Any]:
        """Estimate processing time for a given audio duration."""
        try:
            # Rough estimates based on typical performance
            transcription_time = duration_minutes * 0.3  # ~30% of audio duration for Whisper
            llm_time = 30  # ~30 seconds for LLM analysis
            overhead = 30  # File handling, preprocessing, etc.
            
            total_estimate = transcription_time + llm_time + overhead
            
            return {
                "estimated_total_seconds": int(total_estimate),
                "estimated_total_minutes": round(total_estimate / 60, 1),
                "breakdown": {
                    "transcription_seconds": int(transcription_time),
                    "llm_analysis_seconds": llm_time,
                    "overhead_seconds": overhead
                },
                "note": "Estimates may vary based on system performance and audio quality"
            }
            
        except Exception as e:
            logger.error(f"Failed to estimate processing time: {e}")
            return {"error": str(e)}
