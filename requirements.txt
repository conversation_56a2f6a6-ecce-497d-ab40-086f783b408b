# mFAssistant Dependencies
# For step-by-step installation, use: python install.py

# Core Dependencies (install first)
python-telegram-bot==20.8
requests>=2.28.0
pydub>=0.25.0
python-dotenv>=1.0.0
aiofiles>=23.0.0
PyYAML>=6.0

# AI Dependencies (may require special handling)
# Install PyTorch first: pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
# Then: pip install openai-whisper SpeechRecognition

# Uncomment below if installing all at once (may fail on some systems)
# torch>=2.0.0
# torchaudio>=2.0.0
# openai-whisper>=20231117
# SpeechRecognition>=3.10.0
