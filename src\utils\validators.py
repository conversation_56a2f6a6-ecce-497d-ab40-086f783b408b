"""
Validation Utilities

Contains validation functions for files, configurations, and data.
"""

import re
import logging
from pathlib import Path
from typing import List, Optional, Dict, Any

logger = logging.getLogger(__name__)

class ValidationError(Exception):
    """Raised when validation fails."""
    pass

class FileValidator:
    """File validation utilities."""
    
    # Supported audio file extensions
    SUPPORTED_AUDIO_EXTENSIONS = {'.mp3', '.wav', '.m4a', '.aac', '.ogg', '.flac'}
    
    # Supported MIME types
    SUPPORTED_MIME_TYPES = {
        'audio/mpeg',      # MP3
        'audio/wav',       # WAV
        'audio/x-wav',     # WAV (alternative)
        'audio/mp4',       # M4A
        'audio/aac',       # AAC
        'audio/ogg',       # OGG
        'audio/flac',      # FLAC
    }
    
    @classmethod
    def validate_audio_file_extension(cls, filename: str) -> bool:
        """Validate that file has a supported audio extension."""
        if not filename:
            return False
        
        file_path = Path(filename)
        extension = file_path.suffix.lower()
        
        return extension in cls.SUPPORTED_AUDIO_EXTENSIONS
    
    @classmethod
    def validate_mime_type(cls, mime_type: Optional[str]) -> bool:
        """Validate that MIME type is supported."""
        if not mime_type:
            return False
        
        return mime_type.lower() in cls.SUPPORTED_MIME_TYPES
    
    @classmethod
    def validate_file_size(cls, size_bytes: int, max_size_mb: int) -> bool:
        """Validate that file size is within limits."""
        if size_bytes <= 0:
            return False
        
        max_size_bytes = max_size_mb * 1024 * 1024
        return size_bytes <= max_size_bytes
    
    @classmethod
    def validate_filename(cls, filename: str) -> bool:
        """Validate that filename is safe and reasonable."""
        if not filename:
            return False
        
        # Check length
        if len(filename) > 255:
            return False
        
        # Check for dangerous characters
        dangerous_chars = ['<', '>', ':', '"', '|', '?', '*', '\0']
        if any(char in filename for char in dangerous_chars):
            return False
        
        # Check for reserved names (Windows)
        reserved_names = {
            'CON', 'PRN', 'AUX', 'NUL',
            'COM1', 'COM2', 'COM3', 'COM4', 'COM5', 'COM6', 'COM7', 'COM8', 'COM9',
            'LPT1', 'LPT2', 'LPT3', 'LPT4', 'LPT5', 'LPT6', 'LPT7', 'LPT8', 'LPT9'
        }
        
        name_without_ext = Path(filename).stem.upper()
        if name_without_ext in reserved_names:
            return False
        
        return True

class ConfigValidator:
    """Configuration validation utilities."""
    
    @classmethod
    def validate_telegram_token(cls, token: str) -> bool:
        """Validate Telegram bot token format."""
        if not token:
            return False
        
        # Telegram bot token format: <bot_id>:<token>
        # Example: 123456789:ABCdefGHIjklMNOpqrsTUVwxyz
        pattern = r'^\d+:[A-Za-z0-9_-]+$'
        return bool(re.match(pattern, token))
    
    @classmethod
    def validate_group_ids(cls, group_ids: List[str]) -> bool:
        """Validate Telegram group IDs."""
        if not group_ids:
            return True  # Empty list is valid (allows all groups)
        
        for group_id in group_ids:
            # Group IDs are typically negative integers
            try:
                int_id = int(group_id)
                # Group IDs are usually negative and quite large
                if int_id >= 0 or int_id > -1000000000:
                    logger.warning(f"Unusual group ID format: {group_id}")
            except ValueError:
                return False
        
        return True
    
    @classmethod
    def validate_api_key(cls, api_key: str) -> bool:
        """Validate API key format (basic check)."""
        if not api_key:
            return False
        
        # Basic validation - should be non-empty and reasonable length
        if len(api_key) < 10 or len(api_key) > 200:
            return False
        
        # Should not contain obvious placeholder text
        placeholder_texts = ['your_api_key', 'replace_me', 'example', 'test']
        api_key_lower = api_key.lower()
        
        for placeholder in placeholder_texts:
            if placeholder in api_key_lower:
                return False
        
        return True
    
    @classmethod
    def validate_url(cls, url: str) -> bool:
        """Validate URL format."""
        if not url:
            return False
        
        # Basic URL validation
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        return bool(re.match(url_pattern, url))
    
    @classmethod
    def validate_model_name(cls, model_name: str) -> bool:
        """Validate LLM model name format."""
        if not model_name:
            return False
        
        # Model names typically follow format: provider/model-name:variant
        # Example: meta-llama/llama-3.3-70b-instruct:free
        pattern = r'^[a-zA-Z0-9_-]+/[a-zA-Z0-9_.-]+(?::[a-zA-Z0-9_-]+)?$'
        return bool(re.match(pattern, model_name))

class DataValidator:
    """Data validation utilities."""
    
    @classmethod
    def validate_audio_metadata(cls, metadata: Dict[str, Any]) -> List[str]:
        """
        Validate audio metadata and return list of issues found.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        issues = []
        
        # Required fields
        required_fields = ['duration_seconds', 'file_size_mb', 'sample_rate']
        for field in required_fields:
            if field not in metadata:
                issues.append(f"Missing required field: {field}")
        
        # Validate duration
        if 'duration_seconds' in metadata:
            duration = metadata['duration_seconds']
            if not isinstance(duration, (int, float)) or duration <= 0:
                issues.append("Invalid duration: must be positive number")
            elif duration > 7200:  # 2 hours
                issues.append("Duration too long: exceeds 2 hours")
        
        # Validate file size
        if 'file_size_mb' in metadata:
            size = metadata['file_size_mb']
            if not isinstance(size, (int, float)) or size <= 0:
                issues.append("Invalid file size: must be positive number")
        
        # Validate sample rate
        if 'sample_rate' in metadata:
            sample_rate = metadata['sample_rate']
            if not isinstance(sample_rate, int) or sample_rate < 8000:
                issues.append("Invalid sample rate: must be at least 8000 Hz")
        
        # Validate channels
        if 'channels' in metadata:
            channels = metadata['channels']
            if not isinstance(channels, int) or channels < 1 or channels > 8:
                issues.append("Invalid channel count: must be 1-8")
        
        return issues
    
    @classmethod
    def validate_transcription_result(cls, result: str) -> bool:
        """Validate transcription result."""
        if not result or not isinstance(result, str):
            return False
        
        # Should have some meaningful content
        if len(result.strip()) < 10:
            return False
        
        # Should not be just repeated characters or obvious errors
        if len(set(result.strip())) < 3:
            return False
        
        return True
    
    @classmethod
    def validate_llm_response(cls, response: Dict[str, Any]) -> List[str]:
        """
        Validate LLM response structure.
        
        Returns:
            List of validation error messages (empty if valid)
        """
        issues = []
        
        # Check if response is a dictionary
        if not isinstance(response, dict):
            issues.append("Response must be a dictionary")
            return issues
        
        # Expected sections
        expected_sections = ['summary', 'action_items', 'participants']
        for section in expected_sections:
            if section not in response:
                issues.append(f"Missing section: {section}")
        
        # Validate summary
        if 'summary' in response:
            summary = response['summary']
            if not isinstance(summary, str) or len(summary.strip()) < 20:
                issues.append("Summary must be a non-empty string with at least 20 characters")
        
        # Validate action items
        if 'action_items' in response:
            action_items = response['action_items']
            if not isinstance(action_items, list):
                issues.append("Action items must be a list")
            elif len(action_items) > 50:
                issues.append("Too many action items (max 50)")
        
        # Validate participants
        if 'participants' in response:
            participants = response['participants']
            if not isinstance(participants, list):
                issues.append("Participants must be a list")
            elif len(participants) > 20:
                issues.append("Too many participants (max 20)")
        
        return issues

def validate_environment_config() -> List[str]:
    """
    Validate environment configuration.
    
    Returns:
        List of validation error messages (empty if valid)
    """
    issues = []
    
    # This would be called by the settings module
    # Implementation depends on specific requirements
    
    return issues
