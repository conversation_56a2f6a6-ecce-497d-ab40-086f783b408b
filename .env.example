# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=**********************************************
ALLOWED_GROUPS=-4848849338,group_id_2

# OpenRouter Free Tier Configuration
OPENROUTER_API_KEY=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
OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PRIMARY_LLM_MODEL=meta-llama/llama-3.3-70b-instruct:free
FALLBACK_LLM_MODEL=mistralai/mistral-small-3.2-24b:free

# Whisper Configuration
WHISPER_MODEL_SIZE=base
USE_GPU=false
WHISPER_LANGUAGE=auto

# Processing Configuration
MAX_FILE_SIZE_MB=100
PROCESSING_TIMEOUT_MINUTES=10
TEMP_DIR=./temp

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/mfassistant.log
