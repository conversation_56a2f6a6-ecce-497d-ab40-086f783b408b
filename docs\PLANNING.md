# mFAssistant - Meeting File Assistant Planning Document

## Project Overview

mFAssistant is a Telegram-based meeting assistant that processes MP3 audio files to generate comprehensive meeting summaries and action items. The system will automatically transcribe audio, analyze content using LLM, and return formatted markdown reports.

## Core Requirements

1. **Input**: MP3 files uploaded to Telegram group
2. **Processing**: 
   - Convert MP3 to text (transcription)
   - Analyze transcribed text with LLM
   - Generate meeting summary and to-do list
3. **Output**: Markdown file returned to same Telegram group

## Architecture Design

### High-Level Flow
```
MP3 Upload → Telegram Bot → Audio Transcription → LLM Analysis → MD Generation → Telegram Response
```

### Core Components

#### 1. Telegram Bot Handler
- **Purpose**: Interface with Telegram API to receive files and send responses
- **Responsibilities**:
  - Listen for MP3 file uploads in configured groups
  - Download and validate audio files
  - Send processing status updates
  - Upload generated markdown files back to group
- **Technology**: `python-telegram-bot` library

#### 2. Audio Transcription Service
- **Purpose**: Convert MP3 audio to text
- **Selected Solution**: **Local Whisper Installation** (Cost-Free)
  - **Primary**: OpenAI Whisper local installation
    - Pros: No API costs, high accuracy, privacy, offline capability
    - Cons: Requires local compute resources, initial setup complexity
  - **Fallback**: SpeechRecognition library with Google Speech Recognition free tier
    - Pros: Lightweight, easy setup, no local compute requirements
    - Cons: Requires internet, limited daily usage, lower accuracy
- **Implementation**: Local Whisper with fallback to SpeechRecognition library
- **Performance Considerations**:
  - CPU-only processing acceptable for MVP (slower but functional)
  - GPU acceleration optional for better performance
  - File chunking for large audio files to manage memory usage

#### 3. LLM Analysis Service
- **Purpose**: Process transcribed text to generate summaries and action items
- **Selected Solution**: **OpenRouter Free Tier Models**
- **Available Free Models** (as of 2024):
  - Meta Llama 3.3 70B Instruct (free) - Primary choice for quality
  - Mistral Small 3.2 24B (free) - Alternative option
  - Meta Llama 3.2 11B Vision Instruct (free) - Backup option
- **Configuration**: OpenRouter API endpoint with free model selection
- **API Details**:
  - Endpoint: `https://openrouter.ai/api/v1`
  - Authentication: API key required (free tier available)
  - Rate Limits: Up to 200 free requests/day (1000 with $10 credit purchase)
- **Prompt Engineering**:
  - Meeting summary extraction optimized for free model capabilities
  - Action item identification with structured output
  - Participant identification from transcription
  - Key decision points extraction
- **Output Format**: Structured JSON/text for markdown generation
- **Fallback Strategy**: Multiple free models configured for redundancy

#### 4. Markdown Generator
- **Purpose**: Format LLM analysis into readable markdown
- **Template Structure**:
  - Meeting metadata (date, duration, participants)
  - Executive summary
  - Detailed discussion points
  - Action items with assignees and deadlines
  - Next steps

#### 5. Configuration Management
- **Purpose**: Manage API keys, endpoints, and bot settings
- **Implementation**: Environment variables + config file
- **Security**: Sensitive data in environment variables

## Technical Stack

### Core Dependencies
- **Python 3.9+**: Main runtime
- **python-telegram-bot**: Telegram API integration
- **requests**: HTTP client for OpenRouter API calls
- **pydub**: Audio file processing and MP3 handling
- **python-dotenv**: Environment variable management
- **asyncio**: Asynchronous processing
- **openai-whisper**: Local Whisper installation for transcription
- **torch**: PyTorch for Whisper model execution
- **ffmpeg**: Audio processing backend (system dependency)

### Fallback Dependencies
- **SpeechRecognition**: Backup transcription service
- **pyaudio**: Audio input/output for SpeechRecognition
- **google-cloud-speech**: Google Speech API client (free tier)

### Development Dependencies
- **pytest**: Testing framework
- **black**: Code formatting
- **flake8**: Code linting
- **pytest-asyncio**: Async testing support

## File Structure
```
mFAssistant/
├── src/
│   ├── __init__.py
│   ├── bot/
│   │   ├── __init__.py
│   │   ├── telegram_handler.py
│   │   └── message_processor.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── transcription_service.py
│   │   ├── llm_service.py
│   │   └── markdown_generator.py
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py
│   └── utils/
│       ├── __init__.py
│       ├── file_handler.py
│       └── validators.py
├── tests/
│   ├── __init__.py
│   ├── test_bot/
│   ├── test_services/
│   └── test_utils/
├── docs/
│   ├── PLANNING.md
│   ├── TASKS.md
│   └── API.md
├── config/
│   └── config.yaml
├── requirements.txt
├── .env.example
├── .gitignore
├── main.py
└── README.md
```

## Configuration Requirements

### Environment Variables
- `TELEGRAM_BOT_TOKEN`: Bot authentication token
- `OPENROUTER_API_KEY`: OpenRouter API key for free tier access
- `OPENROUTER_API_BASE`: OpenRouter API endpoint (https://openrouter.ai/api/v1)
- `PRIMARY_LLM_MODEL`: Primary free model (meta-llama/llama-3.3-70b-instruct:free)
- `FALLBACK_LLM_MODEL`: Backup free model (mistralai/mistral-small-3.2-24b:free)
- `ALLOWED_GROUPS`: Comma-separated list of allowed Telegram group IDs
- `WHISPER_MODEL_SIZE`: Local Whisper model size (base, small, medium, large)
- `USE_GPU`: Enable GPU acceleration for Whisper (true/false)

### Config File (config.yaml)
- Audio processing settings (sample rate, chunk size)
- Whisper model configuration (model size, language detection)
- OpenRouter model preferences and fallback order
- LLM prompt templates for different meeting types
- Markdown output templates and formatting options
- File size limits and processing timeouts
- Retry logic and error handling settings

## Security Considerations

1. **API Key Management**: Store in environment variables, never in code
2. **File Validation**: Validate file types and sizes before processing
3. **Group Access Control**: Restrict bot to specific Telegram groups
4. **Data Privacy**: Temporary file cleanup after processing
5. **Error Handling**: Avoid exposing internal errors to users

## MVP Scope

### Phase 1 (MVP)
- Basic Telegram bot setup
- MP3 file reception and validation
- Local Whisper installation and integration for transcription
- OpenRouter free tier integration for LLM analysis
- Basic markdown output with meeting summaries and action items
- File upload back to Telegram
- Fallback transcription service (SpeechRecognition) for reliability

### Future Enhancements (Post-MVP)
- Support for multiple audio formats
- Speaker identification and diarization
- Meeting template customization
- Integration with calendar systems
- Multi-language support
- Advanced analytics and reporting

## Risk Assessment

### Technical Risks
1. **Local Processing Performance**: Whisper may be slow on CPU-only systems
   - Mitigation: Implement progress updates, consider model size optimization
2. **OpenRouter Rate Limits**: Free tier has daily request limits (200/day)
   - Mitigation: Implement request queuing and user notification of limits
3. **Large File Processing**: Long meetings may exceed memory or processing limits
   - Mitigation: Audio chunking, streaming processing, and segment analysis
4. **Network Reliability**: OpenRouter API calls may fail
   - Mitigation: Robust error handling, retry logic, and fallback models
5. **Whisper Model Download**: Initial setup requires downloading large models
   - Mitigation: Automated model download with progress indication

### Operational Risks
1. **Resource Usage**: Local Whisper processing may consume significant CPU/memory
   - Mitigation: Resource monitoring, processing queues, and user communication
2. **Free Tier Limitations**: OpenRouter free models may have quality/capability constraints
   - Mitigation: Prompt optimization, multiple model fallbacks, user expectations management
3. **Privacy Concerns**: Sensitive meeting content processed by external LLM service
   - Mitigation: Clear data handling policies, user consent, temporary data storage

## Success Metrics

1. **Functional**: Successfully process MP3 files and generate summaries using free services
2. **Performance**: Process typical 30-minute meeting in under 5 minutes (CPU-dependent)
3. **Reliability**: 90% success rate for valid MP3 files (accounting for free tier limitations)
4. **Cost Efficiency**: Zero ongoing API costs for transcription and LLM analysis
5. **User Experience**: Clear status updates, progress indicators, and error messages

## Next Steps

1. Set up development environment and dependencies
2. Implement basic Telegram bot structure
3. Integrate transcription service
4. Develop LLM analysis pipeline
5. Create markdown generation templates
6. Implement end-to-end testing
7. Deploy and configure production environment
