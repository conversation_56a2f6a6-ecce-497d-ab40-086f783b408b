# mFAssistant Troubleshooting Guide

## Installation Issues

### 1. Dependency Installation Failures

**Problem**: `pip install` fails with build errors or subprocess errors

**Solutions**:
```bash
# Update pip first
python -m pip install --upgrade pip

# Use the step-by-step installer
python install.py

# Or install manually in order:
pip install python-telegram-bot requests pydub python-dotenv aiofiles PyYAML
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu
pip install openai-whisper
pip install SpeechRecognition
```

### 2. PyTorch Installation Issues

**Problem**: PyTorch fails to install or takes too long

**Solutions**:
```bash
# Try CPU-only version
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

# Or use conda
conda install pytorch torchaudio cpuonly -c pytorch

# For older systems, try specific version
pip install torch==2.0.1 torchaudio==2.0.2
```

### 3. Whisper Installation Issues

**Problem**: `openai-whisper` fails to install

**Solutions**:
```bash
# Install from git
pip install git+https://github.com/openai/whisper.git

# Or try older version
pip install openai-whisper==20230314

# Install dependencies separately
pip install tiktoken
pip install openai-whisper
```

### 4. FFmpeg Not Found

**Problem**: `FFmpeg not found` error

**Solutions**:

**Windows**:
```bash
# Using winget
winget install FFmpeg

# Or download from https://ffmpeg.org/download.html
# Add to PATH environment variable
```

**macOS**:
```bash
# Using Homebrew
brew install ffmpeg

# Using MacPorts
sudo port install ffmpeg
```

**Linux**:
```bash
# Ubuntu/Debian
sudo apt update && sudo apt install ffmpeg

# CentOS/RHEL
sudo yum install ffmpeg

# Arch Linux
sudo pacman -S ffmpeg
```

## Configuration Issues

### 1. Environment Variables Not Loading

**Problem**: Bot fails to start with "Missing required environment variables"

**Solutions**:
1. Check `.env` file exists in project root
2. Verify no spaces around `=` in `.env` file:
   ```
   TELEGRAM_BOT_TOKEN=your_token_here
   # NOT: TELEGRAM_BOT_TOKEN = your_token_here
   ```
3. Check file encoding is UTF-8
4. Restart the application after editing `.env`

### 2. Invalid Telegram Bot Token

**Problem**: "Invalid API key" or authentication errors

**Solutions**:
1. Get new token from @BotFather on Telegram
2. Ensure token format: `123456789:ABCdefGHIjklMNOpqrsTUVwxyz`
3. No extra spaces or quotes in `.env` file
4. Bot must be added to the group before use

### 3. OpenRouter API Issues

**Problem**: LLM analysis fails with API errors

**Solutions**:
1. Verify API key at https://openrouter.ai/
2. Check free tier limits (200 requests/day)
3. Ensure model names are correct:
   - `meta-llama/llama-3.3-70b-instruct:free`
   - `mistralai/mistral-small-3.2-24b:free`

## Runtime Issues

### 1. Whisper Model Download Fails

**Problem**: "Failed to load Whisper model" or download errors

**Solutions**:
```bash
# Pre-download models manually
python -c "import whisper; whisper.load_model('base')"

# Try smaller model
# In .env: WHISPER_MODEL_SIZE=tiny

# Check internet connection and disk space
```

### 2. Audio Processing Errors

**Problem**: "Invalid audio file format" or processing failures

**Solutions**:
1. Ensure file is valid MP3/WAV/M4A
2. Check file size (default limit: 100MB)
3. Verify FFmpeg is working: `ffmpeg -version`
4. Try converting file: `ffmpeg -i input.mp3 output.wav`

### 3. Memory Issues

**Problem**: Out of memory errors during processing

**Solutions**:
1. Use smaller Whisper model: `WHISPER_MODEL_SIZE=tiny`
2. Reduce file size or split large files
3. Close other applications
4. Increase virtual memory/swap space

### 4. Slow Processing

**Problem**: Transcription takes very long

**Solutions**:
1. Use smaller Whisper model for faster processing
2. Enable GPU if available: `USE_GPU=true`
3. Reduce audio quality before upload
4. Process shorter segments

## Bot Issues

### 1. Bot Not Responding

**Problem**: Bot doesn't respond to messages or files

**Solutions**:
1. Check bot is running: look for "Starting Telegram bot..." in logs
2. Verify group ID in `ALLOWED_GROUPS`
3. Ensure bot has admin permissions in group
4. Check logs in `logs/mfassistant.log`

### 2. File Upload Issues

**Problem**: Bot receives file but processing fails

**Solutions**:
1. Check file format (MP3, WAV, M4A supported)
2. Verify file size under limit
3. Ensure sufficient disk space in temp directory
4. Check FFmpeg installation

### 3. Permission Errors

**Problem**: "Not authorized for this group" messages

**Solutions**:
1. Get correct group ID: forward message from group to @userinfobot
2. Add group ID to `ALLOWED_GROUPS` in `.env`
3. Restart bot after configuration changes
4. Use negative group IDs for groups (e.g., `-1001234567890`)

## Performance Optimization

### 1. Faster Transcription

```bash
# Use GPU if available
USE_GPU=true

# Use smaller model
WHISPER_MODEL_SIZE=tiny  # or base, small

# Optimize audio preprocessing
# In config.yaml:
audio:
  preprocessing:
    normalize_audio: true
    remove_silence: true
```

### 2. Reduce Memory Usage

```bash
# Use CPU-only PyTorch
pip uninstall torch torchaudio
pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu

# Smaller Whisper model
WHISPER_MODEL_SIZE=tiny

# Limit concurrent processing
# In config.yaml:
processing:
  max_concurrent_jobs: 1
```

## Getting Help

### 1. Check Logs

```bash
# View recent logs
tail -f logs/mfassistant.log

# Search for errors
grep -i error logs/mfassistant.log
```

### 2. Test Components

```bash
# Test configuration
python -c "from src.config.settings import Settings; print('✅ Config OK')"

# Test Whisper
python -c "import whisper; print('✅ Whisper OK')"

# Test Telegram bot
python -c "from src.bot.telegram_handler import TelegramBot; print('✅ Bot OK')"
```

### 3. Debug Mode

```bash
# Enable debug logging
# In .env:
LOG_LEVEL=DEBUG

# Run with verbose output
python main.py
```

### 4. Common Error Messages

| Error | Solution |
|-------|----------|
| `ModuleNotFoundError: No module named 'telegram'` | Install: `pip install python-telegram-bot` |
| `ModuleNotFoundError: No module named 'whisper'` | Install: `pip install openai-whisper` |
| `FileNotFoundError: ffmpeg` | Install FFmpeg (see above) |
| `ConfigurationError: Missing required environment variables` | Check `.env` file |
| `TelegramError: Unauthorized` | Check bot token |
| `LLMError: Invalid API key` | Check OpenRouter API key |

If you continue to have issues, please check the GitHub issues or create a new issue with:
1. Your operating system and Python version
2. Complete error message and stack trace
3. Contents of your `.env` file (with sensitive data removed)
4. Output of `pip list` showing installed packages
