"""
Telegram Bot Handler

Handles Telegram bot functionality including message processing,
file handling, and user interactions.
"""

import logging
import async<PERSON>
from typing import Optional, List
from pathlib import Path

from telegram import Update, Bot
from telegram.ext import (
    Application, 
    CommandHandler, 
    MessageHandler, 
    filters,
    ContextTypes
)
from telegram.error import TelegramError

from config.settings import Settings
from bot.message_processor import MessageProcessor

logger = logging.getLogger(__name__)

class TelegramBot:
    """Main Telegram bot class."""
    
    def __init__(self, settings: Settings):
        """Initialize the Telegram bot."""
        self.settings = settings
        self.application: Optional[Application] = None
        self.message_processor = MessageProcessor(settings)
        self._setup_application()
    
    def _setup_application(self) -> None:
        """Setup the Telegram application with handlers."""
        # Create application
        self.application = Application.builder().token(
            self.settings.telegram_bot_token
        ).build()
        
        # Add command handlers
        self.application.add_handler(
            CommandHandler("start", self.start_command)
        )
        self.application.add_handler(
            CommandHandler("help", self.help_command)
        )
        self.application.add_handler(
            CommandHandler("status", self.status_command)
        )
        
        # Add message handlers
        self.application.add_handler(
            MessageHandler(
                filters.Document.AUDIO | filters.Document.ALL,
                self.handle_document
            )
        )
        self.application.add_handler(
            MessageHandler(filters.AUDIO, self.handle_audio)
        )
        self.application.add_handler(
            MessageHandler(filters.TEXT & ~filters.COMMAND, self.handle_text)
        )
        
        # Add error handler
        self.application.add_error_handler(self.error_handler)
    
    async def start_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /start command."""
        if not self._is_authorized_group(update):
            await update.message.reply_text(
                "❌ This bot is not authorized for this group."
            )
            return
        
        welcome_message = (
            "🎵 **mFAssistant - Meeting File Assistant**\n\n"
            "I can help you process meeting recordings and generate summaries!\n\n"
            "**How to use:**\n"
            "1. Upload an MP3 file to this group\n"
            "2. I'll transcribe the audio using local Whisper\n"
            "3. I'll analyze the content using free LLM models\n"
            "4. You'll receive a markdown summary with action items\n\n"
            "**Commands:**\n"
            "• /help - Show this help message\n"
            "• /status - Check bot status\n\n"
            "**Supported formats:** MP3, WAV, M4A\n"
            f"**Max file size:** {self.settings.max_file_size_mb}MB\n\n"
            "Ready to process your meeting recordings! 🚀"
        )
        
        await update.message.reply_text(
            welcome_message,
            parse_mode='Markdown'
        )
    
    async def help_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /help command."""
        if not self._is_authorized_group(update):
            return
        
        help_message = (
            "🔧 **mFAssistant Help**\n\n"
            "**File Processing:**\n"
            "• Upload MP3, WAV, or M4A files\n"
            f"• Maximum file size: {self.settings.max_file_size_mb}MB\n"
            f"• Processing timeout: {self.settings.processing_timeout_minutes} minutes\n\n"
            "**Features:**\n"
            "• 🎯 Cost-free transcription (local Whisper)\n"
            "• 🤖 Free LLM analysis (OpenRouter free tier)\n"
            "• 📝 Automated meeting summaries\n"
            "• ✅ Action item extraction\n"
            "• 👥 Participant identification\n\n"
            "**Processing Steps:**\n"
            "1. File validation and download\n"
            "2. Audio transcription (may take a few minutes)\n"
            "3. LLM analysis and summarization\n"
            "4. Markdown report generation\n\n"
            "**Tips:**\n"
            "• Clear audio quality improves transcription\n"
            "• Shorter files process faster\n"
            "• Be patient - local processing takes time\n\n"
            "Need support? Check the logs or contact the administrator."
        )
        
        await update.message.reply_text(
            help_message,
            parse_mode='Markdown'
        )
    
    async def status_command(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle /status command."""
        if not self._is_authorized_group(update):
            return
        
        status_message = (
            "📊 **mFAssistant Status**\n\n"
            f"🤖 **Bot Status:** Online ✅\n"
            f"🎵 **Whisper Model:** {self.settings.whisper_model_size}\n"
            f"🧠 **Primary LLM:** {self.settings.primary_llm_model.split('/')[-1]}\n"
            f"💾 **GPU Enabled:** {'Yes' if self.settings.use_gpu else 'No'}\n"
            f"📁 **Max File Size:** {self.settings.max_file_size_mb}MB\n"
            f"⏱️ **Timeout:** {self.settings.processing_timeout_minutes} minutes\n\n"
            f"🔧 **Configuration:** Loaded ✅\n"
            f"📝 **Logging:** Active ✅\n"
            f"🌐 **API Access:** Configured ✅\n\n"
            "Ready to process your files! 🚀"
        )
        
        await update.message.reply_text(
            status_message,
            parse_mode='Markdown'
        )
    
    async def handle_document(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle document uploads (including audio files)."""
        if not self._is_authorized_group(update):
            return
        
        document = update.message.document
        if not document:
            return
        
        # Check if it's an audio file
        if document.mime_type and document.mime_type.startswith('audio/'):
            await self._process_audio_file(update, context, document)
        else:
            await update.message.reply_text(
                "❌ Please upload an audio file (MP3, WAV, M4A)."
            )
    
    async def handle_audio(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle direct audio uploads."""
        if not self._is_authorized_group(update):
            return
        
        audio = update.message.audio
        if audio:
            await self._process_audio_file(update, context, audio)
    
    async def handle_text(self, update: Update, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle text messages."""
        if not self._is_authorized_group(update):
            return
        
        # For now, just acknowledge text messages
        if update.message.text.lower() in ['hello', 'hi', 'hey']:
            await update.message.reply_text(
                "👋 Hello! Please upload an MP3 file for me to process."
            )
    
    async def _process_audio_file(self, update: Update, context: ContextTypes.DEFAULT_TYPE, file_obj) -> None:
        """Process an uploaded audio file."""
        try:
            # Check file size
            file_size_mb = file_obj.file_size / (1024 * 1024) if file_obj.file_size else 0

            if not self.settings.validate_file_size(file_size_mb):
                await update.message.reply_text(
                    f"❌ File too large! Maximum size is {self.settings.max_file_size_mb}MB. "
                    f"Your file is {file_size_mb:.1f}MB."
                )
                return

            # Send initial processing message
            processing_msg = await update.message.reply_text(
                "🎵 **Processing your audio file...**\n\n"
                "📥 Starting processing pipeline...\n"
                "⏳ This may take a few minutes depending on file size.\n\n"
                "I'll update you on the progress!",
                parse_mode='Markdown'
            )

            # Process the file using the message processor
            await self.message_processor.process_audio_file(
                update, context, file_obj, processing_msg
            )

        except Exception as e:
            logger.error(f"Error processing audio file: {e}", exc_info=True)
            await update.message.reply_text(
                "❌ Sorry, there was an error processing your file. Please try again."
            )
    
    def _is_authorized_group(self, update: Update) -> bool:
        """Check if the message is from an authorized group."""
        if not update.message or not update.message.chat:
            return False
        
        chat_id = str(update.message.chat.id)
        allowed_groups = self.settings.allowed_groups
        
        # If no groups specified, allow all (for testing)
        if not allowed_groups:
            logger.warning("No allowed groups specified - allowing all groups")
            return True
        
        is_authorized = chat_id in allowed_groups
        if not is_authorized:
            logger.warning(f"Unauthorized access attempt from group: {chat_id}")
        
        return is_authorized
    
    async def error_handler(self, update: object, context: ContextTypes.DEFAULT_TYPE) -> None:
        """Handle errors."""
        logger.error(f"Exception while handling an update: {context.error}", exc_info=True)
        
        # Try to send error message to user if possible
        if isinstance(update, Update) and update.message:
            try:
                await update.message.reply_text(
                    "❌ An error occurred while processing your request. "
                    "Please try again or contact the administrator."
                )
            except TelegramError:
                pass  # Ignore if we can't send the error message
    
    async def start(self) -> None:
        """Start the bot."""
        if not self.application:
            raise RuntimeError("Application not initialized")
        
        logger.info("Starting Telegram bot...")
        await self.application.initialize()
        await self.application.start()
        await self.application.updater.start_polling()
        
        # Keep the bot running
        try:
            await asyncio.Event().wait()
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt, stopping...")
        finally:
            await self.stop()
    
    async def stop(self) -> None:
        """Stop the bot gracefully."""
        if self.application:
            logger.info("Stopping Telegram bot...")
            await self.application.updater.stop()
            await self.application.stop()
            await self.application.shutdown()
            logger.info("Bot stopped successfully")
