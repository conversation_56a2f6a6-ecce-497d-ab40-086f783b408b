# mFAssistant - Meeting File Assistant Planning Document

## Project Overview

mFAssistant is a Telegram-based meeting assistant that processes MP3 audio files to generate comprehensive meeting summaries and action items. The system will automatically transcribe audio, analyze content using LLM, and return formatted markdown reports.

## Core Requirements

1. **Input**: MP3 files uploaded to Telegram group
2. **Processing**: 
   - Convert MP3 to text (transcription)
   - Analyze transcribed text with LLM
   - Generate meeting summary and to-do list
3. **Output**: Markdown file returned to same Telegram group

## Architecture Design

### High-Level Flow
```
MP3 Upload → Telegram Bot → Audio Transcription → LLM Analysis → MD Generation → Telegram Response
```

### Core Components

#### 1. Telegram Bot Handler
- **Purpose**: Interface with Telegram API to receive files and send responses
- **Responsibilities**:
  - Listen for MP3 file uploads in configured groups
  - Download and validate audio files
  - Send processing status updates
  - Upload generated markdown files back to group
- **Technology**: `python-telegram-bot` library

#### 2. Audio Transcription Service
- **Purpose**: Convert MP3 audio to text
- **Options Considered**:
  - **OpenAI Whisper API** (Recommended for MVP)
    - Pros: High accuracy, good language support, cloud-based
    - Cons: Requires API key, costs per usage
  - **Local Whisper** (Alternative)
    - Pros: No API costs, privacy
    - Cons: Requires GPU for good performance, larger deployment
- **Implementation**: REST API client for Whisper

#### 3. LLM Analysis Service
- **Purpose**: Process transcribed text to generate summaries and action items
- **Configuration**: Configurable endpoint and API key
- **Prompt Engineering**:
  - Meeting summary extraction
  - Action item identification
  - Participant identification
  - Key decision points
- **Output Format**: Structured data for markdown generation

#### 4. Markdown Generator
- **Purpose**: Format LLM analysis into readable markdown
- **Template Structure**:
  - Meeting metadata (date, duration, participants)
  - Executive summary
  - Detailed discussion points
  - Action items with assignees and deadlines
  - Next steps

#### 5. Configuration Management
- **Purpose**: Manage API keys, endpoints, and bot settings
- **Implementation**: Environment variables + config file
- **Security**: Sensitive data in environment variables

## Technical Stack

### Core Dependencies
- **Python 3.9+**: Main runtime
- **python-telegram-bot**: Telegram API integration
- **requests**: HTTP client for API calls
- **pydub**: Audio file processing
- **python-dotenv**: Environment variable management
- **asyncio**: Asynchronous processing

### Optional Dependencies
- **openai**: If using OpenAI Whisper API
- **whisper**: If implementing local transcription
- **pytest**: Testing framework
- **black**: Code formatting

## File Structure
```
mFAssistant/
├── src/
│   ├── __init__.py
│   ├── bot/
│   │   ├── __init__.py
│   │   ├── telegram_handler.py
│   │   └── message_processor.py
│   ├── services/
│   │   ├── __init__.py
│   │   ├── transcription_service.py
│   │   ├── llm_service.py
│   │   └── markdown_generator.py
│   ├── config/
│   │   ├── __init__.py
│   │   └── settings.py
│   └── utils/
│       ├── __init__.py
│       ├── file_handler.py
│       └── validators.py
├── tests/
│   ├── __init__.py
│   ├── test_bot/
│   ├── test_services/
│   └── test_utils/
├── docs/
│   ├── PLANNING.md
│   ├── TASKS.md
│   └── API.md
├── config/
│   └── config.yaml
├── requirements.txt
├── .env.example
├── .gitignore
├── main.py
└── README.md
```

## Configuration Requirements

### Environment Variables
- `TELEGRAM_BOT_TOKEN`: Bot authentication token
- `LLM_API_ENDPOINT`: LLM service endpoint URL
- `LLM_API_KEY`: LLM service authentication key
- `WHISPER_API_KEY`: Transcription service key (if using OpenAI)
- `ALLOWED_GROUPS`: Comma-separated list of allowed Telegram group IDs

### Config File (config.yaml)
- Audio processing settings
- LLM prompt templates
- Markdown output templates
- File size limits
- Processing timeouts

## Security Considerations

1. **API Key Management**: Store in environment variables, never in code
2. **File Validation**: Validate file types and sizes before processing
3. **Group Access Control**: Restrict bot to specific Telegram groups
4. **Data Privacy**: Temporary file cleanup after processing
5. **Error Handling**: Avoid exposing internal errors to users

## MVP Scope

### Phase 1 (MVP)
- Basic Telegram bot setup
- MP3 file reception and validation
- OpenAI Whisper API integration for transcription
- Simple LLM integration for summary generation
- Basic markdown output
- File upload back to Telegram

### Future Enhancements (Post-MVP)
- Support for multiple audio formats
- Speaker identification and diarization
- Meeting template customization
- Integration with calendar systems
- Multi-language support
- Advanced analytics and reporting

## Risk Assessment

### Technical Risks
1. **API Rate Limits**: Whisper and LLM APIs may have usage limits
   - Mitigation: Implement queuing and retry logic
2. **Large File Processing**: Long meetings may exceed API limits
   - Mitigation: Audio chunking and segment processing
3. **Network Reliability**: API calls may fail
   - Mitigation: Robust error handling and user feedback

### Operational Risks
1. **Cost Management**: API usage costs could escalate
   - Mitigation: Usage monitoring and limits
2. **Privacy Concerns**: Sensitive meeting content
   - Mitigation: Clear data handling policies

## Success Metrics

1. **Functional**: Successfully process MP3 files and generate summaries
2. **Performance**: Process typical 30-minute meeting in under 2 minutes
3. **Reliability**: 95% success rate for valid MP3 files
4. **User Experience**: Clear status updates and error messages

## Next Steps

1. Set up development environment and dependencies
2. Implement basic Telegram bot structure
3. Integrate transcription service
4. Develop LLM analysis pipeline
5. Create markdown generation templates
6. Implement end-to-end testing
7. Deploy and configure production environment
