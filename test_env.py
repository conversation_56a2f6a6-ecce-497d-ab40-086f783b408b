#!/usr/bin/env python3
"""
Test Environment Variable Loading
"""

import os
from pathlib import Path

def test_env_loading():
    """Test environment variable loading."""
    print("🔍 Testing environment variable loading...")
    print()
    
    # Check if .env file exists
    env_file = Path('.env')
    print(f"📁 .env file exists: {env_file.exists()}")
    
    if env_file.exists():
        print(f"📁 .env file path: {env_file.absolute()}")
        print(f"📁 .env file size: {env_file.stat().st_size} bytes")
        
        # Read and display file contents (safely)
        try:
            with open(env_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            print("\n📄 .env file contents:")
            print("-" * 40)
            lines = content.split('\n')
            for i, line in enumerate(lines, 1):
                if line.strip():
                    # Hide sensitive values
                    if '=' in line:
                        key, value = line.split('=', 1)
                        if 'TOKEN' in key or 'KEY' in key:
                            display_value = value[:10] + "..." if len(value) > 10 else value
                        else:
                            display_value = value
                        print(f"{i:2d}: {key}={display_value}")
                    else:
                        print(f"{i:2d}: {line}")
                else:
                    print(f"{i:2d}: (empty line)")
            print("-" * 40)
            
        except Exception as e:
            print(f"❌ Error reading .env file: {e}")
            return False
    
    # Test python-dotenv loading
    print("\n🔧 Testing python-dotenv loading...")
    try:
        from dotenv import load_dotenv
        
        # Load .env file
        result = load_dotenv()
        print(f"✅ load_dotenv() result: {result}")
        
        # Check specific variables
        test_vars = ['TELEGRAM_BOT_TOKEN', 'OPENROUTER_API_KEY', 'ALLOWED_GROUPS']
        
        print("\n📋 Environment variables after loading:")
        for var in test_vars:
            value = os.getenv(var)
            if value:
                # Hide sensitive parts
                if 'TOKEN' in var or 'KEY' in var:
                    display_value = value[:10] + "..." if len(value) > 10 else value
                else:
                    display_value = value
                print(f"✅ {var}: {display_value}")
            else:
                print(f"❌ {var}: Not found")
        
        return True
        
    except ImportError:
        print("❌ python-dotenv not installed")
        return False
    except Exception as e:
        print(f"❌ Error loading environment: {e}")
        return False

def test_manual_loading():
    """Test manual environment loading."""
    print("\n🔧 Testing manual .env parsing...")
    
    try:
        env_file = Path('.env')
        if not env_file.exists():
            print("❌ .env file not found")
            return False
        
        # Parse manually
        env_vars = {}
        with open(env_file, 'r', encoding='utf-8') as f:
            for line_num, line in enumerate(f, 1):
                line = line.strip()
                if line and not line.startswith('#') and '=' in line:
                    key, value = line.split('=', 1)
                    key = key.strip()
                    value = value.strip()
                    
                    # Remove quotes if present
                    if (value.startswith('"') and value.endswith('"')) or \
                       (value.startswith("'") and value.endswith("'")):
                        value = value[1:-1]
                    
                    env_vars[key] = value
                    print(f"Line {line_num}: {key} = {value[:10]}..." if len(value) > 10 else f"Line {line_num}: {key} = {value}")
        
        print(f"\n✅ Parsed {len(env_vars)} variables manually")
        
        # Check required variables
        required = ['TELEGRAM_BOT_TOKEN', 'OPENROUTER_API_KEY', 'ALLOWED_GROUPS']
        missing = [var for var in required if var not in env_vars]
        
        if missing:
            print(f"❌ Missing variables: {missing}")
            return False
        else:
            print("✅ All required variables found")
            return True
            
    except Exception as e:
        print(f"❌ Manual parsing failed: {e}")
        return False

def main():
    """Main function."""
    print("=" * 60)
    print("🧪 Environment Variable Test")
    print("=" * 60)
    
    # Test current working directory
    print(f"📁 Current directory: {Path.cwd()}")
    
    # Test environment loading
    env_ok = test_env_loading()
    
    # Test manual parsing
    manual_ok = test_manual_loading()
    
    print("\n" + "=" * 60)
    print("📊 Test Results")
    print("=" * 60)
    
    if env_ok and manual_ok:
        print("✅ Environment loading works correctly")
        print("\nThe issue might be:")
        print("1. The diagnostic script is running from wrong directory")
        print("2. There's a caching issue")
        print("3. Try restarting your terminal/command prompt")
    else:
        print("❌ Environment loading has issues")
        print("\nPossible fixes:")
        print("1. Check .env file format (no spaces around =)")
        print("2. Save .env file with UTF-8 encoding")
        print("3. Make sure .env is in the project root directory")
        print("4. Try recreating the .env file")

if __name__ == "__main__":
    main()
