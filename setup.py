#!/usr/bin/env python3
"""
mFAssistant Setup Script

Helps users set up the mFAssistant environment and verify configuration.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Print setup header."""
    print("=" * 60)
    print("🎵 mFAssistant - Meeting File Assistant Setup")
    print("=" * 60)
    print()

def check_python_version():
    """Check Python version compatibility."""
    print("📋 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python 3.9+ required. Current version: {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def check_system_dependencies():
    """Check system dependencies."""
    print("\n🔧 Checking system dependencies...")
    
    # Check FFmpeg
    try:
        result = subprocess.run(['ffmpeg', '-version'], 
                              capture_output=True, text=True, timeout=10)
        if result.returncode == 0:
            print("✅ FFmpeg - Available")
        else:
            print("❌ FFmpeg - Not working properly")
            return False
    except (subprocess.TimeoutExpired, FileNotFoundError):
        print("❌ FFmpeg - Not found")
        print("   Please install FFmpeg:")
        if platform.system() == "Windows":
            print("   - Download from https://ffmpeg.org/download.html")
            print("   - Or use: winget install FFmpeg")
        elif platform.system() == "Darwin":  # macOS
            print("   - brew install ffmpeg")
        else:  # Linux
            print("   - sudo apt install ffmpeg  (Ubuntu/Debian)")
            print("   - sudo yum install ffmpeg  (CentOS/RHEL)")
        return False
    
    return True

def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ['logs', 'temp', 'config']
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(exist_ok=True)
            print(f"✅ Created: {directory}/")
        else:
            print(f"✅ Exists: {directory}/")

def check_env_file():
    """Check and create .env file."""
    print("\n🔐 Checking environment configuration...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            # Copy example file
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ Created .env file from .env.example")
            print("⚠️  Please edit .env file with your API keys!")
        else:
            print("❌ .env.example file not found")
            return False
    else:
        print("✅ .env file exists")
    
    # Check for required variables
    required_vars = [
        'TELEGRAM_BOT_TOKEN',
        'OPENROUTER_API_KEY',
        'ALLOWED_GROUPS'
    ]
    
    missing_vars = []
    with open(env_file, 'r') as f:
        content = f.read()
        for var in required_vars:
            if f"{var}=your_" in content or f"{var}=" not in content:
                missing_vars.append(var)
    
    if missing_vars:
        print(f"⚠️  Please configure these variables in .env:")
        for var in missing_vars:
            print(f"   - {var}")
        return False
    
    print("✅ Environment variables configured")
    return True

def install_dependencies():
    """Install Python dependencies."""
    print("\n📦 Installing Python dependencies...")
    
    try:
        # Check if we're in a virtual environment
        in_venv = hasattr(sys, 'real_prefix') or (
            hasattr(sys, 'base_prefix') and sys.base_prefix != sys.prefix
        )
        
        if not in_venv:
            print("⚠️  Virtual environment not detected")
            print("   Recommended: python -m venv venv && source venv/bin/activate")
            response = input("   Continue anyway? (y/N): ")
            if response.lower() != 'y':
                return False
        
        # Install requirements
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', '-r', 'requirements.txt'
        ], capture_output=True, text=True)
        
        if result.returncode == 0:
            print("✅ Dependencies installed successfully")
            return True
        else:
            print("❌ Failed to install dependencies:")
            print(result.stderr)
            return False
            
    except Exception as e:
        print(f"❌ Error installing dependencies: {e}")
        return False

def run_basic_tests():
    """Run basic configuration tests."""
    print("\n🧪 Running basic tests...")
    
    try:
        # Test imports
        sys.path.insert(0, str(Path('src')))
        
        from config.settings import Settings
        print("✅ Configuration module - OK")
        
        from utils.file_handler import AudioFileHandler
        print("✅ File handler module - OK")
        
        from services.transcription_service import TranscriptionService
        print("✅ Transcription service - OK")
        
        from services.llm_service import LLMService
        print("✅ LLM service - OK")
        
        from bot.telegram_handler import TelegramBot
        print("✅ Telegram bot - OK")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def print_next_steps():
    """Print next steps for the user."""
    print("\n🚀 Setup Complete! Next Steps:")
    print()
    print("1. Configure your API keys in .env file:")
    print("   - Get Telegram bot token from @BotFather")
    print("   - Get OpenRouter API key from https://openrouter.ai/")
    print("   - Add your Telegram group IDs to ALLOWED_GROUPS")
    print()
    print("2. Test the configuration:")
    print("   python -c \"from src.config.settings import Settings; print('Config OK')\"")
    print()
    print("3. Run the bot:")
    print("   python main.py")
    print()
    print("4. Add the bot to your Telegram group and upload an MP3 file!")
    print()
    print("📚 For more information, see README.md")

def main():
    """Main setup function."""
    print_header()
    
    success = True
    
    # Run all checks
    success &= check_python_version()
    success &= check_system_dependencies()
    
    if success:
        create_directories()
        success &= check_env_file()
        success &= install_dependencies()
        success &= run_basic_tests()
    
    print("\n" + "=" * 60)
    
    if success:
        print("🎉 Setup completed successfully!")
        print_next_steps()
    else:
        print("❌ Setup failed. Please fix the issues above and try again.")
        sys.exit(1)

if __name__ == "__main__":
    main()
