# mFAssistant - Meeting File Assistant

A cost-free Telegram-based meeting assistant that processes MP3 audio files to generate comprehensive meeting summaries and action items.

## Features

- 🎵 **MP3 Audio Processing**: Upload MP3 files directly to Telegram
- 🗣️ **Cost-Free Transcription**: Local Whisper integration with SpeechRecognition fallback
- 🤖 **Free LLM Analysis**: OpenRouter free tier models for meeting analysis
- 📝 **Markdown Reports**: Automated generation of meeting summaries and action items
- 🔒 **Privacy-Focused**: Local processing with minimal external API usage
- 📱 **Telegram Integration**: Seamless bot interaction in group chats

## Architecture

- **Transcription**: Local OpenAI Whisper installation (primary) + SpeechRecognition (fallback)
- **LLM Analysis**: OpenRouter free tier (Llama 3.3 70B, Mistral Small 3.2 24B)
- **Bot Framework**: python-telegram-bot
- **Audio Processing**: pydub + ffmpeg

## Quick Start

### Automated Setup (Recommended)

1. **Clone and Run Setup**:
   ```bash
   git clone <repository-url>
   cd mFAssistant
   python setup.py
   ```

The setup script will:
- Check Python version and system dependencies
- Create necessary directories
- Install Python dependencies
- Create and validate configuration files
- Run basic tests

### Manual Setup

1. **Clone and Setup Environment**:
   ```bash
   git clone <repository-url>
   cd mFAssistant
   python -m venv venv
   source venv/bin/activate  # On Windows: venv\Scripts\activate
   pip install -r requirements.txt
   ```

2. **Install System Dependencies**:
   - **FFmpeg**: Required for audio processing
     - Windows: `winget install FFmpeg` or download from https://ffmpeg.org/
     - macOS: `brew install ffmpeg`
     - Linux: `sudo apt install ffmpeg` (Ubuntu/Debian)

3. **Configure Environment**:
   ```bash
   cp .env.example .env
   # Edit .env with your API keys and settings
   ```

4. **Get API Keys**:
   - **Telegram Bot Token**: Message @BotFather on Telegram
   - **OpenRouter API Key**: Sign up at https://openrouter.ai/ (free tier available)
   - **Group IDs**: Add bot to groups and get group IDs

5. **Run the Bot**:
   ```bash
   python main.py
   ```

## Configuration

### Required Environment Variables

- `TELEGRAM_BOT_TOKEN`: Your Telegram bot token from @BotFather
- `OPENROUTER_API_KEY`: Your OpenRouter API key for free tier access
- `ALLOWED_GROUPS`: Comma-separated list of allowed Telegram group IDs

### Optional Configuration

- `WHISPER_MODEL_SIZE`: Whisper model size (base, small, medium, large)
- `USE_GPU`: Enable GPU acceleration for Whisper (true/false)
- `MAX_FILE_SIZE_MB`: Maximum MP3 file size limit
- `PROCESSING_TIMEOUT_MINUTES`: Processing timeout limit

## Usage

1. Add the bot to your Telegram group
2. Upload an MP3 file to the group
3. The bot will process the audio and return a markdown summary
4. Review the generated meeting notes and action items

## Development

### Running Tests
```bash
pytest
pytest --cov  # With coverage
```

### Code Formatting
```bash
black .
flake8 .
mypy .
```

### Project Structure
```
mFAssistant/
├── src/
│   ├── bot/           # Telegram bot handlers
│   ├── services/      # Core business logic
│   ├── config/        # Configuration management
│   └── utils/         # Utility functions
├── tests/             # Test suite
├── docs/              # Documentation
└── config/            # Configuration files
```

## Cost-Free Design

This project is designed to minimize ongoing costs:

- **Transcription**: Local Whisper processing (no API costs)
- **LLM Analysis**: OpenRouter free tier models (200+ requests/day)
- **Infrastructure**: Self-hosted bot (no cloud hosting costs)

## Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests for new functionality
5. Submit a pull request

## License

[Add your license here]

## Support

For issues and questions, please use the GitHub issue tracker.
