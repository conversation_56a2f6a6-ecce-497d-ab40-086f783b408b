"""
Compatibility wrapper for Python 3.13
"""

import sys
import logging

logger = logging.getLogger(__name__)

def create_telegram_bot(settings):
    """Create telegram bot with compatibility handling."""
    try:
        # Try the minimal bot first (best for Python 3.13)
        from bot.minimal_bot import MinimalTelegramBot
        logger.info("Using minimal bot for Python 3.13 compatibility")
        return MinimalTelegramBot(settings)
    except Exception as e:
        logger.warning(f"Minimal bot failed: {e}")

        try:
            # Try the simple bot
            from bot.simple_bot import SimpleTelegramBot
            logger.info("Using simplified bot")
            return SimpleTelegramBot(settings)
        except Exception as e2:
            logger.warning(f"Simple bot failed: {e2}")

            try:
                # Fallback to original bot
                from bot.telegram_handler import TelegramBot
                logger.info("Using original bot implementation")
                return TelegramBot(settings)
            except Exception as e3:
                logger.error(f"All bot implementations failed: {e3}")
                raise RuntimeError(f"Cannot create telegram bot: {e3}")
