"""
LLM Service

Provides meeting analysis and summarization using OpenRouter free tier models.
"""

import logging
import asyncio
import json
from typing import Dict, Any, List, Optional, Callable
from datetime import datetime, timedelta
import aiohttp

from config.settings import Settings
from utils.validators import DataValidator

logger = logging.getLogger(__name__)

class LLMError(Exception):
    """Raised when LLM processing fails."""
    pass

class RateLimitError(LLMError):
    """Raised when rate limit is exceeded."""
    pass

class LLMService:
    """Handles LLM analysis using OpenRouter free tier models."""
    
    def __init__(self, settings: Settings):
        """Initialize the LLM service."""
        self.settings = settings
        self.session: Optional[aiohttp.ClientSession] = None
        self.request_count = 0
        self.daily_reset_time = datetime.now().replace(hour=0, minute=0, second=0, microsecond=0)
        self.last_request_time = None
        
        # Model configuration
        self.models = [
            self.settings.primary_llm_model,
            self.settings.fallback_llm_model,
            "meta-llama/llama-3.2-11b-vision-instruct:free"  # Backup model
        ]
    
    async def __aenter__(self):
        """Async context manager entry."""
        await self._ensure_session()
        return self
    
    async def __aexit__(self, exc_type, exc_val, exc_tb):
        """Async context manager exit."""
        await self._close_session()
    
    async def _ensure_session(self) -> None:
        """Ensure aiohttp session is available."""
        if self.session is None or self.session.closed:
            timeout = aiohttp.ClientTimeout(total=300)  # 5 minute timeout
            self.session = aiohttp.ClientSession(timeout=timeout)
    
    async def _close_session(self) -> None:
        """Close aiohttp session."""
        if self.session and not self.session.closed:
            await self.session.close()
    
    async def analyze_meeting(
        self, 
        transcription: str, 
        metadata: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Analyze meeting transcription and generate summary.
        
        Args:
            transcription: Meeting transcription text
            metadata: Meeting metadata
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary containing meeting analysis
        """
        try:
            await self._ensure_session()
            
            if progress_callback:
                await progress_callback("🧠 Starting LLM analysis...")
            
            # Check rate limits
            await self._check_rate_limits()
            
            # Prepare analysis prompt
            prompt = self._create_analysis_prompt(transcription, metadata)
            
            # Try models in order until one succeeds
            for i, model in enumerate(self.models):
                try:
                    if progress_callback:
                        await progress_callback(f"🤖 Trying model: {model.split('/')[-1]}...")
                    
                    result = await self._call_openrouter_api(model, prompt)
                    
                    if result:
                        # Parse and validate result
                        analysis = self._parse_analysis_result(result, model)
                        
                        if progress_callback:
                            await progress_callback("✅ LLM analysis completed successfully")
                        
                        logger.info(f"Meeting analysis completed using model: {model}")
                        return analysis
                
                except RateLimitError:
                    if progress_callback:
                        await progress_callback("⚠️ Rate limit reached, trying next model...")
                    continue
                except Exception as e:
                    logger.warning(f"Model {model} failed: {e}")
                    if i < len(self.models) - 1:
                        if progress_callback:
                            await progress_callback(f"⚠️ Model failed, trying next model...")
                        continue
                    else:
                        raise
            
            raise LLMError("All models failed to process the request")
            
        except Exception as e:
            logger.error(f"Meeting analysis failed: {e}")
            raise LLMError(f"Failed to analyze meeting: {e}")
    
    async def _call_openrouter_api(self, model: str, prompt: str) -> Dict[str, Any]:
        """Call OpenRouter API with specified model."""
        try:
            headers = {
                "Authorization": f"Bearer {self.settings.openrouter_api_key}",
                "Content-Type": "application/json",
                "HTTP-Referer": "https://github.com/mfassistant/mfassistant",
                "X-Title": "mFAssistant"
            }
            
            payload = {
                "model": model,
                "messages": [
                    {
                        "role": "system",
                        "content": "You are a professional meeting assistant that analyzes meeting transcriptions and creates structured summaries."
                    },
                    {
                        "role": "user", 
                        "content": prompt
                    }
                ],
                "temperature": self.settings.llm_temperature,
                "max_tokens": self.settings.llm_max_tokens,
                "top_p": 0.9,
                "frequency_penalty": 0.0,
                "presence_penalty": 0.0
            }
            
            url = f"{self.settings.openrouter_api_base}/chat/completions"
            
            logger.debug(f"Calling OpenRouter API: {model}")
            
            async with self.session.post(url, headers=headers, json=payload) as response:
                self._update_request_tracking()
                
                if response.status == 429:
                    raise RateLimitError("Rate limit exceeded")
                elif response.status == 401:
                    raise LLMError("Invalid API key")
                elif response.status == 402:
                    raise LLMError("Insufficient credits")
                elif response.status != 200:
                    error_text = await response.text()
                    raise LLMError(f"API error {response.status}: {error_text}")
                
                result = await response.json()
                
                if "choices" not in result or not result["choices"]:
                    raise LLMError("No response from model")
                
                content = result["choices"][0]["message"]["content"]
                
                # Log usage information if available
                if "usage" in result:
                    usage = result["usage"]
                    logger.info(f"Token usage - Prompt: {usage.get('prompt_tokens', 0)}, "
                               f"Completion: {usage.get('completion_tokens', 0)}, "
                               f"Total: {usage.get('total_tokens', 0)}")
                
                return {"content": content, "model": model, "usage": result.get("usage", {})}
                
        except aiohttp.ClientError as e:
            raise LLMError(f"Network error: {e}")
        except json.JSONDecodeError as e:
            raise LLMError(f"Invalid JSON response: {e}")
    
    def _create_analysis_prompt(self, transcription: str, metadata: Dict[str, Any]) -> str:
        """Create analysis prompt optimized for free tier models."""
        
        # Truncate transcription if too long to fit in context window
        max_transcription_length = 8000  # Conservative limit for free models
        if len(transcription) > max_transcription_length:
            transcription = transcription[:max_transcription_length] + "... [truncated]"
        
        prompt = f"""
Please analyze this meeting transcription and provide a structured summary in JSON format.

MEETING METADATA:
- Duration: {metadata.get('duration_minutes', 'unknown')} minutes
- File size: {metadata.get('file_size_mb', 'unknown')} MB
- Date: {metadata.get('created_at', 'unknown')}

TRANSCRIPTION:
{transcription}

Please provide your analysis in the following JSON format:
{{
    "executive_summary": "Brief 2-3 sentence overview of the meeting",
    "key_points": [
        "Important point 1",
        "Important point 2",
        "Important point 3"
    ],
    "action_items": [
        {{
            "task": "Description of action item",
            "assignee": "Person responsible (if mentioned)",
            "deadline": "Deadline if mentioned",
            "priority": "high/medium/low"
        }}
    ],
    "participants": [
        "List of people mentioned in the meeting"
    ],
    "decisions_made": [
        "Key decisions that were made"
    ],
    "next_steps": [
        "What happens next"
    ],
    "topics_discussed": [
        "Main topics covered"
    ]
}}

Requirements:
- Keep summaries concise but informative
- Extract specific action items with assignees when possible
- Identify all participants mentioned
- Focus on actionable outcomes
- Use clear, professional language
- Ensure valid JSON format
"""
        
        return prompt
    
    def _parse_analysis_result(self, result: Dict[str, Any], model: str) -> Dict[str, Any]:
        """Parse and validate LLM analysis result."""
        try:
            content = result.get("content", "")
            
            # Try to extract JSON from the response
            json_start = content.find("{")
            json_end = content.rfind("}") + 1
            
            if json_start == -1 or json_end == 0:
                raise ValueError("No JSON found in response")
            
            json_content = content[json_start:json_end]
            analysis = json.loads(json_content)
            
            # Validate required fields
            required_fields = ["executive_summary", "key_points", "action_items", "participants"]
            for field in required_fields:
                if field not in analysis:
                    analysis[field] = []
            
            # Validate structure
            validation_issues = DataValidator.validate_llm_response(analysis)
            if validation_issues:
                logger.warning(f"Analysis validation issues: {validation_issues}")
            
            # Add metadata
            analysis["metadata"] = {
                "model_used": model,
                "generated_at": datetime.now().isoformat(),
                "token_usage": result.get("usage", {}),
                "validation_issues": validation_issues
            }
            
            return analysis
            
        except json.JSONDecodeError as e:
            logger.error(f"Failed to parse JSON response: {e}")
            # Return a basic structure if parsing fails
            return {
                "executive_summary": "Failed to parse meeting analysis",
                "key_points": ["Analysis parsing failed"],
                "action_items": [],
                "participants": [],
                "decisions_made": [],
                "next_steps": [],
                "topics_discussed": [],
                "metadata": {
                    "model_used": model,
                    "generated_at": datetime.now().isoformat(),
                    "error": str(e),
                    "raw_content": result.get("content", "")[:500]
                }
            }
    
    async def _check_rate_limits(self) -> None:
        """Check and enforce rate limits."""
        now = datetime.now()
        
        # Reset daily counter if needed
        if now >= self.daily_reset_time + timedelta(days=1):
            self.request_count = 0
            self.daily_reset_time = now.replace(hour=0, minute=0, second=0, microsecond=0)
        
        # Check daily limit
        if self.request_count >= self.settings.llm_requests_per_day:
            raise RateLimitError(f"Daily request limit exceeded ({self.settings.llm_requests_per_day})")
        
        # Check rate limiting (requests per minute)
        if self.last_request_time:
            time_since_last = (now - self.last_request_time).total_seconds()
            min_interval = 60 / 10  # 10 requests per minute max
            
            if time_since_last < min_interval:
                wait_time = min_interval - time_since_last
                logger.info(f"Rate limiting: waiting {wait_time:.1f} seconds")
                await asyncio.sleep(wait_time)
    
    def _update_request_tracking(self) -> None:
        """Update request tracking counters."""
        self.request_count += 1
        self.last_request_time = datetime.now()
        
        logger.debug(f"Request count: {self.request_count}/{self.settings.llm_requests_per_day}")
    
    def get_service_status(self) -> Dict[str, Any]:
        """Get service status information."""
        now = datetime.now()
        time_until_reset = (self.daily_reset_time + timedelta(days=1)) - now
        
        return {
            "available_models": self.models,
            "primary_model": self.settings.primary_llm_model,
            "requests_today": self.request_count,
            "daily_limit": self.settings.llm_requests_per_day,
            "requests_remaining": max(0, self.settings.llm_requests_per_day - self.request_count),
            "time_until_reset": str(time_until_reset).split('.')[0],  # Remove microseconds
            "last_request": self.last_request_time.isoformat() if self.last_request_time else None,
            "session_active": self.session is not None and not self.session.closed
        }
