#!/usr/bin/env python3
"""
mFAssistant Installation Script

Simplified installation script that handles dependencies step by step.
"""

import os
import sys
import subprocess
import platform
from pathlib import Path

def print_header():
    """Print installation header."""
    print("=" * 60)
    print("🎵 mFAssistant - Installation Script")
    print("=" * 60)
    print()

def check_python_version():
    """Check Python version compatibility."""
    print("📋 Checking Python version...")
    
    version = sys.version_info
    if version.major < 3 or (version.major == 3 and version.minor < 9):
        print(f"❌ Python 3.9+ required. Current version: {version.major}.{version.minor}")
        return False
    
    print(f"✅ Python {version.major}.{version.minor}.{version.micro} - Compatible")
    return True

def create_directories():
    """Create necessary directories."""
    print("\n📁 Creating directories...")
    
    directories = ['logs', 'temp']
    
    for directory in directories:
        path = Path(directory)
        if not path.exists():
            path.mkdir(exist_ok=True)
            print(f"✅ Created: {directory}/")
        else:
            print(f"✅ Exists: {directory}/")

def setup_env_file():
    """Setup environment file."""
    print("\n🔐 Setting up environment file...")
    
    env_file = Path('.env')
    env_example = Path('.env.example')
    
    if not env_file.exists():
        if env_example.exists():
            with open(env_example, 'r') as src, open(env_file, 'w') as dst:
                dst.write(src.read())
            print("✅ Created .env file from .env.example")
        else:
            # Create basic .env file
            env_content = """# Telegram Bot Configuration
TELEGRAM_BOT_TOKEN=your_telegram_bot_token_here
ALLOWED_GROUPS=your_group_id_here

# OpenRouter Free Tier Configuration
OPENROUTER_API_KEY=your_openrouter_api_key_here
OPENROUTER_API_BASE=https://openrouter.ai/api/v1
PRIMARY_LLM_MODEL=meta-llama/llama-3.3-70b-instruct:free
FALLBACK_LLM_MODEL=mistralai/mistral-small-3.2-24b:free

# Whisper Configuration
WHISPER_MODEL_SIZE=base
USE_GPU=false
WHISPER_LANGUAGE=auto

# Processing Configuration
MAX_FILE_SIZE_MB=100
PROCESSING_TIMEOUT_MINUTES=10
TEMP_DIR=./temp

# Logging Configuration
LOG_LEVEL=INFO
LOG_FILE=./logs/mfassistant.log
"""
            with open(env_file, 'w') as f:
                f.write(env_content)
            print("✅ Created basic .env file")
    else:
        print("✅ .env file already exists")

def install_core_dependencies():
    """Install core dependencies."""
    print("\n📦 Installing core dependencies...")
    
    core_packages = [
        "python-telegram-bot>=20.0,<21.0",
        "requests>=2.28.0",
        "pydub>=0.25.0", 
        "python-dotenv>=1.0.0",
        "aiofiles>=23.0.0",
        "PyYAML>=6.0"
    ]
    
    for package in core_packages:
        try:
            print(f"Installing {package.split('>=')[0]}...")
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', package
            ], capture_output=True, text=True, timeout=120)
            
            if result.returncode == 0:
                print(f"✅ {package.split('>=')[0]} installed")
            else:
                print(f"⚠️ {package.split('>=')[0]} failed: {result.stderr[:100]}...")
                return False
                
        except subprocess.TimeoutExpired:
            print(f"⚠️ {package.split('>=')[0]} installation timed out")
            return False
        except Exception as e:
            print(f"⚠️ {package.split('>=')[0]} error: {e}")
            return False
    
    return True

def install_ai_dependencies():
    """Install AI/ML dependencies."""
    print("\n🤖 Installing AI dependencies...")
    print("⚠️ This may take several minutes and download large files...")
    
    # Install PyTorch first (CPU version for compatibility)
    try:
        print("Installing PyTorch (CPU version)...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 
            'torch', 'torchaudio', '--index-url', 'https://download.pytorch.org/whl/cpu'
        ], capture_output=True, text=True, timeout=600)
        
        if result.returncode == 0:
            print("✅ PyTorch installed")
        else:
            print("⚠️ PyTorch installation failed, trying alternative...")
            # Try without index URL
            result = subprocess.run([
                sys.executable, '-m', 'pip', 'install', 'torch', 'torchaudio'
            ], capture_output=True, text=True, timeout=600)
            
            if result.returncode == 0:
                print("✅ PyTorch installed (alternative method)")
            else:
                print("❌ PyTorch installation failed")
                return False
                
    except subprocess.TimeoutExpired:
        print("⚠️ PyTorch installation timed out")
        return False
    
    # Install Whisper
    try:
        print("Installing OpenAI Whisper...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'openai-whisper'
        ], capture_output=True, text=True, timeout=300)
        
        if result.returncode == 0:
            print("✅ OpenAI Whisper installed")
        else:
            print(f"❌ Whisper installation failed: {result.stderr[:100]}...")
            return False
            
    except subprocess.TimeoutExpired:
        print("⚠️ Whisper installation timed out")
        return False
    
    # Install SpeechRecognition
    try:
        print("Installing SpeechRecognition...")
        result = subprocess.run([
            sys.executable, '-m', 'pip', 'install', 'SpeechRecognition'
        ], capture_output=True, text=True, timeout=120)
        
        if result.returncode == 0:
            print("✅ SpeechRecognition installed")
        else:
            print("⚠️ SpeechRecognition failed (fallback transcription may not work)")
            
    except Exception:
        print("⚠️ SpeechRecognition failed (fallback transcription may not work)")
    
    return True

def test_imports():
    """Test that core modules can be imported."""
    print("\n🧪 Testing imports...")
    
    test_modules = [
        ('telegram', 'python-telegram-bot'),
        ('requests', 'requests'),
        ('pydub', 'pydub'),
        ('dotenv', 'python-dotenv'),
        ('yaml', 'PyYAML'),
        ('whisper', 'openai-whisper'),
        ('torch', 'torch')
    ]
    
    success = True
    for module, package in test_modules:
        try:
            __import__(module)
            print(f"✅ {package} - OK")
        except ImportError:
            print(f"❌ {package} - Failed")
            success = False
    
    return success

def print_next_steps():
    """Print next steps."""
    print("\n🚀 Installation Complete! Next Steps:")
    print()
    print("1. Configure your API keys in .env file:")
    print("   - Get Telegram bot token from @BotFather")
    print("   - Get OpenRouter API key from https://openrouter.ai/")
    print("   - Add your Telegram group IDs")
    print()
    print("2. Test the installation:")
    print("   python -c \"from src.config.settings import Settings; print('✅ Config OK')\"")
    print()
    print("3. Run the bot:")
    print("   python main.py")
    print()
    print("4. If you encounter issues:")
    print("   - Check FFmpeg is installed: ffmpeg -version")
    print("   - Verify .env file has correct values")
    print("   - Check logs in logs/mfassistant.log")

def main():
    """Main installation function."""
    print_header()
    
    if not check_python_version():
        sys.exit(1)
    
    create_directories()
    setup_env_file()
    
    print("\n" + "=" * 60)
    print("Installing dependencies in stages...")
    print("=" * 60)
    
    if not install_core_dependencies():
        print("\n❌ Core dependency installation failed.")
        print("Try running: pip install --upgrade pip")
        print("Then run this script again.")
        sys.exit(1)
    
    if not install_ai_dependencies():
        print("\n⚠️ AI dependency installation failed.")
        print("You can try installing manually:")
        print("pip install torch torchaudio --index-url https://download.pytorch.org/whl/cpu")
        print("pip install openai-whisper")
        response = input("Continue anyway? (y/N): ")
        if response.lower() != 'y':
            sys.exit(1)
    
    print("\n" + "=" * 60)
    
    if test_imports():
        print("🎉 Installation completed successfully!")
        print_next_steps()
    else:
        print("⚠️ Installation completed with some issues.")
        print("Some features may not work properly.")
        print("Check the failed imports above and install manually if needed.")

if __name__ == "__main__":
    main()
