"""
Compatibility wrapper for Python 3.13
"""

import sys
import logging

logger = logging.getLogger(__name__)

def create_telegram_bot(settings):
    """Create telegram bot with compatibility handling."""
    try:
        # Try the simple bot first
        from bot.simple_bot import SimpleTelegramBot
        logger.info("Using simplified bot for Python 3.13 compatibility")
        return SimpleTelegramBot(settings)
    except Exception as e:
        logger.warning(f"Simple bot failed: {e}")
        
        try:
            # Fallback to original bot
            from bot.telegram_handler import TelegramBot
            logger.info("Using original bot implementation")
            return TelegramBot(settings)
        except Exception as e2:
            logger.error(f"Both bot implementations failed: {e2}")
            raise RuntimeError(f"Cannot create telegram bot: {e2}")
