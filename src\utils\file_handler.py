"""
File Handler Utilities

Handles file operations including download, validation, temporary storage,
and metadata extraction for audio files.
"""

import os
import logging
import asyncio
import tempfile
import hashlib
from pathlib import Path
from typing import Optional, Dict, Any, Tuple, List
from datetime import datetime

import aiofiles
from pydub import AudioSegment
from pydub.utils import mediainfo
from telegram import File as TelegramFile

from config.settings import Settings

logger = logging.getLogger(__name__)

class FileProcessingError(Exception):
    """Raised when file processing fails."""
    pass

class AudioFileHandler:
    """Handles audio file operations."""
    
    def __init__(self, settings: Settings):
        """Initialize the file handler."""
        self.settings = settings
        self.temp_dir = Path(settings.temp_dir)
        self.temp_dir.mkdir(exist_ok=True)
    
    async def download_and_validate_file(
        self, 
        telegram_file: TelegramFile,
        original_filename: str
    ) -> Tuple[Path, Dict[str, Any]]:
        """
        Download and validate a Telegram file.
        
        Returns:
            Tuple of (file_path, metadata)
        """
        try:
            # Generate unique filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            file_hash = hashlib.md5(f"{telegram_file.file_id}{timestamp}".encode()).hexdigest()[:8]
            safe_filename = self._sanitize_filename(original_filename)
            temp_filename = f"{timestamp}_{file_hash}_{safe_filename}"
            temp_path = self.temp_dir / temp_filename
            
            logger.info(f"Downloading file: {original_filename} -> {temp_path}")
            
            # Download file
            await telegram_file.download_to_drive(temp_path)
            
            # Validate file
            await self._validate_audio_file(temp_path)
            
            # Extract metadata
            metadata = await self._extract_metadata(temp_path, original_filename)
            
            logger.info(f"File downloaded and validated successfully: {temp_path}")
            return temp_path, metadata
            
        except Exception as e:
            # Clean up on error
            if 'temp_path' in locals() and temp_path.exists():
                temp_path.unlink()
            raise FileProcessingError(f"Failed to download and validate file: {e}")
    
    async def _validate_audio_file(self, file_path: Path) -> None:
        """Validate that the file is a valid audio file."""
        if not file_path.exists():
            raise FileProcessingError("File does not exist")
        
        # Check file size
        file_size_mb = file_path.stat().st_size / (1024 * 1024)
        if not self.settings.validate_file_size(file_size_mb):
            raise FileProcessingError(
                f"File too large: {file_size_mb:.1f}MB (max: {self.settings.max_file_size_mb}MB)"
            )
        
        # Check file format by trying to load it
        try:
            # Use pydub to validate audio format
            audio = AudioSegment.from_file(str(file_path))
            
            # Basic audio validation
            if len(audio) == 0:
                raise FileProcessingError("Audio file is empty")
            
            if len(audio) < 1000:  # Less than 1 second
                raise FileProcessingError("Audio file is too short (minimum 1 second)")
            
            # Check duration (optional limit)
            duration_minutes = len(audio) / (1000 * 60)
            max_duration = self.settings.processing_timeout_minutes * 2  # Allow 2x timeout for duration
            if duration_minutes > max_duration:
                logger.warning(f"Audio file is very long: {duration_minutes:.1f} minutes")
            
        except Exception as e:
            raise FileProcessingError(f"Invalid audio file format: {e}")
    
    async def _extract_metadata(self, file_path: Path, original_filename: str) -> Dict[str, Any]:
        """Extract metadata from audio file."""
        try:
            # Get file stats
            file_stats = file_path.stat()
            file_size_mb = file_stats.st_size / (1024 * 1024)
            
            # Get audio metadata using pydub
            audio = AudioSegment.from_file(str(file_path))
            
            # Get detailed media info
            media_info = mediainfo(str(file_path))
            
            metadata = {
                "original_filename": original_filename,
                "file_path": str(file_path),
                "file_size_mb": round(file_size_mb, 2),
                "file_size_bytes": file_stats.st_size,
                "duration_seconds": len(audio) / 1000,
                "duration_minutes": round(len(audio) / (1000 * 60), 2),
                "sample_rate": audio.frame_rate,
                "channels": audio.channels,
                "frame_count": audio.frame_count(),
                "format": media_info.get("format_name", "unknown"),
                "bit_rate": media_info.get("bit_rate", "unknown"),
                "created_at": datetime.now().isoformat(),
                "file_hash": self._calculate_file_hash(file_path)
            }
            
            logger.info(f"Extracted metadata: duration={metadata['duration_minutes']:.1f}min, "
                       f"size={metadata['file_size_mb']}MB, format={metadata['format']}")
            
            return metadata
            
        except Exception as e:
            logger.error(f"Error extracting metadata: {e}")
            # Return basic metadata on error
            return {
                "original_filename": original_filename,
                "file_path": str(file_path),
                "file_size_mb": round(file_path.stat().st_size / (1024 * 1024), 2),
                "error": str(e),
                "created_at": datetime.now().isoformat()
            }
    
    def _calculate_file_hash(self, file_path: Path) -> str:
        """Calculate SHA256 hash of file."""
        try:
            hash_sha256 = hashlib.sha256()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_sha256.update(chunk)
            return hash_sha256.hexdigest()
        except Exception as e:
            logger.error(f"Error calculating file hash: {e}")
            return "unknown"
    
    def _sanitize_filename(self, filename: str) -> str:
        """Sanitize filename for safe storage."""
        # Remove or replace unsafe characters
        unsafe_chars = '<>:"/\\|?*'
        safe_filename = filename
        
        for char in unsafe_chars:
            safe_filename = safe_filename.replace(char, '_')
        
        # Limit length
        if len(safe_filename) > 100:
            name, ext = os.path.splitext(safe_filename)
            safe_filename = name[:95] + ext
        
        return safe_filename
    
    async def preprocess_audio(self, file_path: Path, target_path: Optional[Path] = None) -> Path:
        """
        Preprocess audio file for better transcription results.
        
        Args:
            file_path: Input audio file path
            target_path: Output path (optional, will generate if not provided)
            
        Returns:
            Path to preprocessed audio file
        """
        try:
            if target_path is None:
                target_path = file_path.parent / f"preprocessed_{file_path.name}"
            
            logger.info(f"Preprocessing audio: {file_path} -> {target_path}")
            
            # Load audio
            audio = AudioSegment.from_file(str(file_path))
            
            # Apply preprocessing based on config
            config = self.settings._config_data.get("audio", {}).get("preprocessing", {})
            
            if config.get("normalize_audio", True):
                # Normalize audio levels
                audio = audio.normalize()
                logger.debug("Applied audio normalization")
            
            if config.get("remove_silence", True):
                # Remove silence from beginning and end
                audio = audio.strip_silence(silence_thresh=-40, silence_len=1000)
                logger.debug("Removed silence")
            
            # Convert to optimal format for Whisper (16kHz, mono)
            target_sample_rate = self.settings.audio_sample_rate
            if audio.frame_rate != target_sample_rate:
                audio = audio.set_frame_rate(target_sample_rate)
                logger.debug(f"Resampled to {target_sample_rate}Hz")
            
            if audio.channels > 1:
                audio = audio.set_channels(1)  # Convert to mono
                logger.debug("Converted to mono")
            
            # Export preprocessed audio
            audio.export(str(target_path), format="wav")
            
            logger.info(f"Audio preprocessing completed: {target_path}")
            return target_path
            
        except Exception as e:
            logger.error(f"Error preprocessing audio: {e}")
            # Return original file if preprocessing fails
            return file_path
    
    def cleanup_file(self, file_path: Path) -> None:
        """Clean up temporary file."""
        try:
            if file_path.exists():
                file_path.unlink()
                logger.debug(f"Cleaned up file: {file_path}")
        except Exception as e:
            logger.error(f"Error cleaning up file {file_path}: {e}")
    
    def cleanup_temp_files(self, max_age_hours: int = 24) -> None:
        """Clean up old temporary files."""
        try:
            current_time = datetime.now().timestamp()
            max_age_seconds = max_age_hours * 3600
            
            cleaned_count = 0
            for file_path in self.temp_dir.iterdir():
                if file_path.is_file():
                    file_age = current_time - file_path.stat().st_mtime
                    if file_age > max_age_seconds:
                        file_path.unlink()
                        cleaned_count += 1
            
            if cleaned_count > 0:
                logger.info(f"Cleaned up {cleaned_count} old temporary files")
                
        except Exception as e:
            logger.error(f"Error during temp file cleanup: {e}")
    
    async def chunk_audio_file(self, file_path: Path, chunk_duration_seconds: Optional[int] = None) -> List[Path]:
        """
        Split large audio file into smaller chunks for processing.
        
        Args:
            file_path: Input audio file
            chunk_duration_seconds: Duration of each chunk (uses config default if None)
            
        Returns:
            List of chunk file paths
        """
        try:
            chunk_duration = chunk_duration_seconds or self.settings.audio_chunk_duration
            chunk_duration_ms = chunk_duration * 1000
            
            audio = AudioSegment.from_file(str(file_path))
            
            # If audio is shorter than chunk duration, return original file
            if len(audio) <= chunk_duration_ms:
                return [file_path]
            
            chunks = []
            chunk_count = 0
            
            for i in range(0, len(audio), chunk_duration_ms):
                chunk = audio[i:i + chunk_duration_ms]
                chunk_path = file_path.parent / f"chunk_{chunk_count:03d}_{file_path.name}"
                
                chunk.export(str(chunk_path), format="wav")
                chunks.append(chunk_path)
                chunk_count += 1
            
            logger.info(f"Split audio into {len(chunks)} chunks of {chunk_duration}s each")
            return chunks
            
        except Exception as e:
            logger.error(f"Error chunking audio file: {e}")
            return [file_path]  # Return original file on error
