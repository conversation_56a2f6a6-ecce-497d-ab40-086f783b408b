"""
Transcription Service

Provides cost-free audio transcription using local Whisper with SpeechRecognition fallback.
"""

import logging
import asyncio
import tempfile
from pathlib import Path
from typing import Optional, Dict, Any, List, Callable
from datetime import datetime

import whisper
import speech_recognition as sr
import torch
from pydub import AudioSegment

from config.settings import Settings
from utils.validators import DataValidator
from utils.file_handler import AudioFileHandler

logger = logging.getLogger(__name__)

class TranscriptionError(Exception):
    """Raised when transcription fails."""
    pass

class TranscriptionService:
    """Handles audio transcription using local Whisper and fallback services."""
    
    def __init__(self, settings: Settings):
        """Initialize the transcription service."""
        self.settings = settings
        self._whisper_model = None
        self._speech_recognizer = None
        self._model_loading = False
        
        # Initialize speech recognition fallback
        self._init_speech_recognition()
    
    def _init_speech_recognition(self) -> None:
        """Initialize SpeechRecognition fallback."""
        try:
            self._speech_recognizer = sr.Recognizer()
            logger.info("SpeechRecognition fallback initialized")
        except Exception as e:
            logger.error(f"Failed to initialize SpeechRecognition: {e}")
    
    async def load_whisper_model(self, progress_callback: Optional[Callable] = None) -> bool:
        """
        Load Whisper model asynchronously.
        
        Args:
            progress_callback: Optional callback for progress updates
            
        Returns:
            True if model loaded successfully, False otherwise
        """
        if self._whisper_model is not None:
            return True
        
        if self._model_loading:
            # Wait for ongoing loading to complete
            while self._model_loading:
                await asyncio.sleep(1)
            return self._whisper_model is not None
        
        self._model_loading = True
        
        try:
            model_size = self.settings.whisper_model_size
            device = "cuda" if self.settings.use_gpu and torch.cuda.is_available() else "cpu"
            
            if progress_callback:
                await progress_callback(f"Loading Whisper model '{model_size}' on {device}...")
            
            logger.info(f"Loading Whisper model: {model_size} on {device}")
            
            # Load model in thread pool to avoid blocking
            loop = asyncio.get_event_loop()
            self._whisper_model = await loop.run_in_executor(
                None, 
                lambda: whisper.load_model(model_size, device=device)
            )
            
            logger.info(f"Whisper model loaded successfully: {model_size}")
            
            if progress_callback:
                await progress_callback(f"✅ Whisper model '{model_size}' loaded successfully")
            
            return True
            
        except Exception as e:
            logger.error(f"Failed to load Whisper model: {e}")
            if progress_callback:
                await progress_callback(f"❌ Failed to load Whisper model: {e}")
            return False
        finally:
            self._model_loading = False
    
    async def transcribe_audio(
        self, 
        audio_path: Path, 
        metadata: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Transcribe audio file using local Whisper with fallback.
        
        Args:
            audio_path: Path to audio file
            metadata: Audio file metadata
            progress_callback: Optional callback for progress updates
            
        Returns:
            Dictionary containing transcription results
        """
        start_time = datetime.now()
        
        try:
            # Validate input
            if not audio_path.exists():
                raise TranscriptionError(f"Audio file not found: {audio_path}")
            
            # Try Whisper first
            result = await self._transcribe_with_whisper(audio_path, metadata, progress_callback)
            
            if result and DataValidator.validate_transcription_result(result.get('text', '')):
                result['method'] = 'whisper'
                result['processing_time'] = (datetime.now() - start_time).total_seconds()
                return result
            
            # Fallback to SpeechRecognition
            if progress_callback:
                await progress_callback("🔄 Whisper failed, trying fallback transcription...")
            
            result = await self._transcribe_with_speech_recognition(audio_path, metadata, progress_callback)
            result['method'] = 'speech_recognition'
            result['processing_time'] = (datetime.now() - start_time).total_seconds()
            return result
            
        except Exception as e:
            logger.error(f"Transcription failed: {e}")
            raise TranscriptionError(f"All transcription methods failed: {e}")
    
    async def _transcribe_with_whisper(
        self, 
        audio_path: Path, 
        metadata: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Optional[Dict[str, Any]]:
        """Transcribe using local Whisper."""
        try:
            # Load model if not already loaded
            if not await self.load_whisper_model(progress_callback):
                return None
            
            if progress_callback:
                await progress_callback("🎯 Starting Whisper transcription...")
            
            # Prepare transcription options
            options = {
                "language": None if self.settings.whisper_language == "auto" else self.settings.whisper_language,
                "task": "transcribe",
                "temperature": self.settings.get_config("whisper.temperature", 0.0),
                "best_of": self.settings.get_config("whisper.best_of", 5),
                "beam_size": self.settings.get_config("whisper.beam_size", 5),
                "patience": self.settings.get_config("whisper.patience", 1.0),
                "length_penalty": self.settings.get_config("whisper.length_penalty", 1.0),
                "condition_on_previous_text": self.settings.get_config("whisper.condition_on_previous_text", True),
                "fp16": self.settings.get_config("whisper.fp16", True) and torch.cuda.is_available(),
                "compression_ratio_threshold": self.settings.get_config("whisper.compression_ratio_threshold", 2.4),
                "logprob_threshold": self.settings.get_config("whisper.logprob_threshold", -1.0),
                "no_speech_threshold": self.settings.get_config("whisper.no_speech_threshold", 0.6)
            }
            
            # Remove None values
            options = {k: v for k, v in options.items() if v is not None}
            
            logger.info(f"Transcribing with Whisper: {audio_path}")
            
            # Run transcription in thread pool
            loop = asyncio.get_event_loop()
            result = await loop.run_in_executor(
                None,
                lambda: self._whisper_model.transcribe(str(audio_path), **options)
            )
            
            if progress_callback:
                await progress_callback("✅ Whisper transcription completed")
            
            # Process result
            transcription_result = {
                "text": result.get("text", "").strip(),
                "language": result.get("language", "unknown"),
                "segments": result.get("segments", []),
                "confidence": self._calculate_average_confidence(result.get("segments", [])),
                "word_count": len(result.get("text", "").split()),
                "options_used": options
            }
            
            logger.info(f"Whisper transcription completed: {len(transcription_result['text'])} characters, "
                       f"confidence: {transcription_result['confidence']:.2f}")
            
            return transcription_result
            
        except Exception as e:
            logger.error(f"Whisper transcription failed: {e}")
            return None
    
    async def _transcribe_with_speech_recognition(
        self, 
        audio_path: Path, 
        metadata: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """Transcribe using SpeechRecognition library as fallback."""
        try:
            if not self._speech_recognizer:
                raise TranscriptionError("SpeechRecognition not available")
            
            if progress_callback:
                await progress_callback("🎤 Starting fallback transcription...")
            
            # Convert to WAV format if needed
            wav_path = await self._convert_to_wav(audio_path)
            
            # Load audio file
            with sr.AudioFile(str(wav_path)) as source:
                # Adjust for ambient noise
                self._speech_recognizer.adjust_for_ambient_noise(source, duration=1)
                audio_data = self._speech_recognizer.record(source)
            
            if progress_callback:
                await progress_callback("🌐 Processing with Google Speech Recognition...")
            
            # Try Google Speech Recognition (free tier)
            try:
                loop = asyncio.get_event_loop()
                text = await loop.run_in_executor(
                    None,
                    lambda: self._speech_recognizer.recognize_google(audio_data)
                )
                
                if progress_callback:
                    await progress_callback("✅ Fallback transcription completed")
                
                result = {
                    "text": text.strip(),
                    "language": "unknown",
                    "segments": [],
                    "confidence": 0.8,  # Estimated confidence for Google API
                    "word_count": len(text.split()),
                    "fallback_method": "google"
                }
                
                logger.info(f"SpeechRecognition transcription completed: {len(text)} characters")
                return result
                
            except sr.UnknownValueError:
                raise TranscriptionError("Could not understand audio")
            except sr.RequestError as e:
                raise TranscriptionError(f"Speech recognition service error: {e}")
            
        except Exception as e:
            logger.error(f"SpeechRecognition transcription failed: {e}")
            raise TranscriptionError(f"Fallback transcription failed: {e}")
        finally:
            # Clean up temporary WAV file
            if 'wav_path' in locals() and wav_path != audio_path and wav_path.exists():
                wav_path.unlink()
    
    async def _convert_to_wav(self, audio_path: Path) -> Path:
        """Convert audio file to WAV format for SpeechRecognition."""
        if audio_path.suffix.lower() == '.wav':
            return audio_path
        
        try:
            wav_path = audio_path.parent / f"temp_{audio_path.stem}.wav"
            
            # Convert using pydub
            audio = AudioSegment.from_file(str(audio_path))
            audio = audio.set_frame_rate(16000).set_channels(1)  # Optimize for speech recognition
            audio.export(str(wav_path), format="wav")
            
            return wav_path
            
        except Exception as e:
            logger.error(f"Failed to convert audio to WAV: {e}")
            return audio_path  # Return original if conversion fails
    
    def _calculate_average_confidence(self, segments: List[Dict]) -> float:
        """Calculate average confidence from Whisper segments."""
        if not segments:
            return 0.0
        
        # Whisper doesn't provide confidence directly, estimate from other metrics
        total_confidence = 0.0
        total_duration = 0.0
        
        for segment in segments:
            duration = segment.get('end', 0) - segment.get('start', 0)
            if duration > 0:
                # Estimate confidence based on segment characteristics
                # This is a heuristic since Whisper doesn't provide direct confidence scores
                confidence = 0.9  # Base confidence
                
                # Adjust based on segment length (longer segments often more reliable)
                if duration > 5:
                    confidence += 0.05
                elif duration < 1:
                    confidence -= 0.1
                
                # Adjust based on text length vs duration (reasonable speech rate)
                text_length = len(segment.get('text', ''))
                if text_length > 0:
                    speech_rate = text_length / duration
                    if 10 <= speech_rate <= 50:  # Reasonable speech rate
                        confidence += 0.05
                    else:
                        confidence -= 0.1
                
                confidence = max(0.0, min(1.0, confidence))
                total_confidence += confidence * duration
                total_duration += duration
        
        return total_confidence / total_duration if total_duration > 0 else 0.0
    
    async def transcribe_chunks(
        self, 
        chunk_paths: List[Path], 
        metadata: Dict[str, Any],
        progress_callback: Optional[Callable] = None
    ) -> Dict[str, Any]:
        """
        Transcribe multiple audio chunks and combine results.
        
        Args:
            chunk_paths: List of audio chunk file paths
            metadata: Original file metadata
            progress_callback: Optional callback for progress updates
            
        Returns:
            Combined transcription results
        """
        try:
            if len(chunk_paths) == 1:
                return await self.transcribe_audio(chunk_paths[0], metadata, progress_callback)
            
            if progress_callback:
                await progress_callback(f"📝 Transcribing {len(chunk_paths)} audio chunks...")
            
            all_segments = []
            combined_text = []
            total_confidence = 0.0
            total_word_count = 0
            processing_methods = set()
            
            for i, chunk_path in enumerate(chunk_paths):
                if progress_callback:
                    await progress_callback(f"Processing chunk {i+1}/{len(chunk_paths)}...")
                
                chunk_result = await self.transcribe_audio(chunk_path, metadata)
                
                if chunk_result and chunk_result.get('text'):
                    combined_text.append(chunk_result['text'])
                    all_segments.extend(chunk_result.get('segments', []))
                    total_confidence += chunk_result.get('confidence', 0.0)
                    total_word_count += chunk_result.get('word_count', 0)
                    processing_methods.add(chunk_result.get('method', 'unknown'))
            
            # Combine results
            final_text = ' '.join(combined_text).strip()
            avg_confidence = total_confidence / len(chunk_paths) if chunk_paths else 0.0
            
            result = {
                "text": final_text,
                "language": "auto-detected",
                "segments": all_segments,
                "confidence": avg_confidence,
                "word_count": total_word_count,
                "chunk_count": len(chunk_paths),
                "methods_used": list(processing_methods)
            }
            
            if progress_callback:
                await progress_callback(f"✅ All chunks processed: {len(final_text)} characters total")
            
            logger.info(f"Chunk transcription completed: {len(chunk_paths)} chunks, "
                       f"{len(final_text)} characters, confidence: {avg_confidence:.2f}")
            
            return result
            
        except Exception as e:
            logger.error(f"Chunk transcription failed: {e}")
            raise TranscriptionError(f"Failed to transcribe chunks: {e}")
    
    def get_model_info(self) -> Dict[str, Any]:
        """Get information about loaded models."""
        info = {
            "whisper_model_loaded": self._whisper_model is not None,
            "whisper_model_size": self.settings.whisper_model_size,
            "device": "cuda" if self.settings.use_gpu and torch.cuda.is_available() else "cpu",
            "speech_recognition_available": self._speech_recognizer is not None,
            "cuda_available": torch.cuda.is_available()
        }
        
        if self._whisper_model:
            info["whisper_model_type"] = type(self._whisper_model).__name__
        
        return info
