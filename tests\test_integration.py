"""
Integration Tests

Basic integration tests to verify the system components work together.
"""

import pytest
import asyncio
import tempfile
from pathlib import Path
from unittest.mock import Mock, AsyncMock, patch

import sys
sys.path.insert(0, str(Path(__file__).parent.parent / "src"))

from config.settings import Settings, ConfigurationError
from utils.file_handler import AudioFileHandler
from services.transcription_service import TranscriptionService
from services.llm_service import LLMService
from services.markdown_generator import MarkdownGenerator
from bot.message_processor import MessageProcessor

class TestConfiguration:
    """Test configuration management."""
    
    def test_settings_initialization(self):
        """Test that settings can be initialized."""
        # This will fail if required env vars are missing, which is expected
        try:
            settings = Settings()
            assert settings is not None
        except ConfigurationError:
            # Expected when env vars are not set
            pass
    
    def test_config_file_loading(self):
        """Test config file loading."""
        with tempfile.NamedTemporaryFile(mode='w', suffix='.yaml', delete=False) as f:
            f.write("""
audio:
  sample_rate: 16000
  chunk_duration_seconds: 30

llm:
  temperature: 0.7
  max_tokens: 2048
""")
            config_path = f.name
        
        try:
            # Mock environment variables
            with patch.dict('os.environ', {
                'TELEGRAM_BOT_TOKEN': 'test_token',
                'OPENROUTER_API_KEY': 'test_key',
                'ALLOWED_GROUPS': 'test_group'
            }):
                settings = Settings(config_file=config_path)
                assert settings.audio_sample_rate == 16000
                assert settings.llm_temperature == 0.7
        finally:
            Path(config_path).unlink()

class TestFileHandling:
    """Test file handling operations."""
    
    @pytest.fixture
    def settings(self):
        """Mock settings for testing."""
        with patch.dict('os.environ', {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'OPENROUTER_API_KEY': 'test_key',
            'ALLOWED_GROUPS': 'test_group',
            'TEMP_DIR': str(tempfile.gettempdir())
        }):
            return Settings()
    
    def test_file_handler_initialization(self, settings):
        """Test file handler initialization."""
        handler = AudioFileHandler(settings)
        assert handler.settings == settings
        assert handler.temp_dir.exists()
    
    def test_filename_sanitization(self, settings):
        """Test filename sanitization."""
        handler = AudioFileHandler(settings)
        
        # Test dangerous characters
        unsafe_name = "test<>file|name?.mp3"
        safe_name = handler._sanitize_filename(unsafe_name)
        assert "<" not in safe_name
        assert ">" not in safe_name
        assert "|" not in safe_name
        assert "?" not in safe_name
        
        # Test length limiting
        long_name = "a" * 200 + ".mp3"
        safe_name = handler._sanitize_filename(long_name)
        assert len(safe_name) <= 100

class TestTranscriptionService:
    """Test transcription service."""
    
    @pytest.fixture
    def settings(self):
        """Mock settings for testing."""
        with patch.dict('os.environ', {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'OPENROUTER_API_KEY': 'test_key',
            'ALLOWED_GROUPS': 'test_group',
            'WHISPER_MODEL_SIZE': 'tiny'  # Use smallest model for testing
        }):
            return Settings()
    
    def test_transcription_service_initialization(self, settings):
        """Test transcription service initialization."""
        service = TranscriptionService(settings)
        assert service.settings == settings
        assert service._speech_recognizer is not None
    
    def test_model_info(self, settings):
        """Test getting model information."""
        service = TranscriptionService(settings)
        info = service.get_model_info()
        
        assert "whisper_model_loaded" in info
        assert "whisper_model_size" in info
        assert "device" in info
        assert "speech_recognition_available" in info

class TestLLMService:
    """Test LLM service."""
    
    @pytest.fixture
    def settings(self):
        """Mock settings for testing."""
        with patch.dict('os.environ', {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'OPENROUTER_API_KEY': 'test_key',
            'ALLOWED_GROUPS': 'test_group'
        }):
            return Settings()
    
    def test_llm_service_initialization(self, settings):
        """Test LLM service initialization."""
        service = LLMService(settings)
        assert service.settings == settings
        assert len(service.models) > 0
    
    def test_prompt_creation(self, settings):
        """Test analysis prompt creation."""
        service = LLMService(settings)
        
        transcription = "This is a test meeting transcription."
        metadata = {"duration_minutes": 5, "file_size_mb": 2}
        
        prompt = service._create_analysis_prompt(transcription, metadata)
        
        assert "meeting transcription" in prompt.lower()
        assert "json format" in prompt.lower()
        assert transcription in prompt
        assert "5 minutes" in prompt
    
    def test_service_status(self, settings):
        """Test getting service status."""
        service = LLMService(settings)
        status = service.get_service_status()
        
        assert "available_models" in status
        assert "primary_model" in status
        assert "requests_today" in status
        assert "daily_limit" in status

class TestMarkdownGenerator:
    """Test markdown generation."""
    
    @pytest.fixture
    def settings(self):
        """Mock settings for testing."""
        with patch.dict('os.environ', {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'OPENROUTER_API_KEY': 'test_key',
            'ALLOWED_GROUPS': 'test_group'
        }):
            return Settings()
    
    @pytest.fixture
    def sample_analysis(self):
        """Sample analysis data for testing."""
        return {
            "executive_summary": "This was a productive meeting about project planning.",
            "key_points": [
                "Discussed project timeline",
                "Assigned team responsibilities",
                "Set next meeting date"
            ],
            "action_items": [
                {
                    "task": "Complete project proposal",
                    "assignee": "John",
                    "deadline": "Next Friday",
                    "priority": "high"
                }
            ],
            "participants": ["John", "Jane", "Bob"],
            "decisions_made": ["Approved budget increase"],
            "next_steps": ["Schedule follow-up meeting"],
            "topics_discussed": ["Budget", "Timeline", "Resources"],
            "metadata": {
                "model_used": "meta-llama/llama-3.3-70b-instruct:free",
                "generated_at": "2024-01-01T12:00:00"
            }
        }
    
    @pytest.fixture
    def sample_metadata(self):
        """Sample metadata for testing."""
        return {
            "original_filename": "test_meeting.mp3",
            "duration_minutes": 15.5,
            "file_size_mb": 5.2,
            "format": "mp3",
            "created_at": "2024-01-01T12:00:00"
        }
    
    def test_markdown_generator_initialization(self, settings):
        """Test markdown generator initialization."""
        generator = MarkdownGenerator(settings)
        assert generator.settings == settings
    
    def test_report_generation(self, settings, sample_analysis, sample_metadata):
        """Test complete report generation."""
        generator = MarkdownGenerator(settings)
        
        transcription = "This is the meeting transcription text."
        
        report = generator.generate_meeting_report(
            sample_analysis, sample_metadata, transcription
        )
        
        # Check that report contains expected sections
        assert "# Meeting Report" in report
        assert "Executive Summary" in report
        assert "Key Points" in report
        assert "Action Items" in report
        assert "Participants" in report
        assert sample_analysis["executive_summary"] in report
        assert "John" in report  # Participant
        assert "Complete project proposal" in report  # Action item
    
    def test_filename_generation(self, settings, sample_metadata):
        """Test report filename generation."""
        generator = MarkdownGenerator(settings)
        
        filename = generator.generate_filename(sample_metadata)
        
        assert filename.endswith(".md")
        assert "meeting_report" in filename
        assert "test_meeting" in filename

class TestMessageProcessor:
    """Test message processing pipeline."""
    
    @pytest.fixture
    def settings(self):
        """Mock settings for testing."""
        with patch.dict('os.environ', {
            'TELEGRAM_BOT_TOKEN': 'test_token',
            'OPENROUTER_API_KEY': 'test_key',
            'ALLOWED_GROUPS': 'test_group'
        }):
            return Settings()
    
    def test_message_processor_initialization(self, settings):
        """Test message processor initialization."""
        processor = MessageProcessor(settings)
        
        assert processor.settings == settings
        assert processor.file_handler is not None
        assert processor.transcription_service is not None
        assert processor.markdown_generator is not None
    
    @pytest.mark.asyncio
    async def test_processing_status(self, settings):
        """Test getting processing status."""
        processor = MessageProcessor(settings)
        
        status = await processor.get_processing_status()
        
        assert "transcription" in status
        assert "file_handler" in status
        assert "processing" in status
    
    @pytest.mark.asyncio
    async def test_time_estimation(self, settings):
        """Test processing time estimation."""
        processor = MessageProcessor(settings)
        
        estimate = await processor.estimate_processing_time(10.0)  # 10 minutes
        
        assert "estimated_total_seconds" in estimate
        assert "estimated_total_minutes" in estimate
        assert "breakdown" in estimate
        assert estimate["estimated_total_seconds"] > 0

if __name__ == "__main__":
    pytest.main([__file__])
