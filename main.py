"""
mFAssistant Main Entry Point

Starts the Telegram bot and initializes all services.
"""

import asyncio
import logging
import signal
import sys
from pathlib import Path

# Add src to Python path
sys.path.insert(0, str(Path(__file__).parent / "src"))

from config.settings import get_settings, ConfigurationError
from bot.telegram_handler import TelegramBot

def setup_signal_handlers(bot: TelegramBot) -> None:
    """Setup signal handlers for graceful shutdown."""
    def signal_handler(signum, frame):
        logging.info(f"Received signal {signum}, shutting down gracefully...")
        asyncio.create_task(bot.stop())
        sys.exit(0)
    
    signal.signal(signal.SIGINT, signal_handler)
    signal.signal(signal.SIGTERM, signal_handler)

async def main():
    """Main application entry point."""
    try:
        # Load settings
        settings = get_settings()
        logging.info("mFAssistant starting up...")
        logging.info(f"Using Whisper model: {settings.whisper_model_size}")
        logging.info(f"Primary LLM model: {settings.primary_llm_model}")
        
        # Initialize and start bot
        bot = TelegramBot(settings)
        setup_signal_handlers(bot)
        
        logging.info("Starting Telegram bot...")
        await bot.start()
        
    except ConfigurationError as e:
        logging.error(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Unexpected error: {e}", exc_info=True)
        sys.exit(1)

if __name__ == "__main__":
    asyncio.run(main())
