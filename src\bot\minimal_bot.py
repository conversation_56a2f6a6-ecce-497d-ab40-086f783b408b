"""
Minimal Telegram Bot

A minimal implementation that works around Python 3.13 compatibility issues.
"""

import logging
import asyncio
from typing import Optional, Any, Dict

from telegram import Update, Bo<PERSON>
from telegram.ext import ContextTypes
from telegram.error import TelegramError

from config.settings import Settings
from bot.message_processor import MessageProcessor

logger = logging.getLogger(__name__)

class MinimalTelegramBot:
    """Minimal Telegram bot that bypasses Application/Updater issues."""
    
    def __init__(self, settings: Settings):
        """Initialize the bot."""
        self.settings = settings
        self.bot: Optional[Bot] = None
        self.message_processor = MessageProcessor(settings)
        self._running = False
        self._offset = 0
    
    async def setup(self) -> bool:
        """Setup the bot."""
        try:
            # Create bot instance directly (bypassing Application)
            self.bot = Bot(token=self.settings.telegram_bot_token)
            
            # Test the bot
            me = await self.bot.get_me()
            logger.info(f"Bot initialized: @{me.username} ({me.first_name})")
            
            return True
            
        except Exception as e:
            logger.error(f"Bot setup failed: {e}")
            return False
    
    async def handle_update(self, update: Update) -> None:
        """Handle a single update."""
        try:
            if not update.message:
                return
            
            # Check authorization
            if not self._is_authorized_group(update):
                await update.message.reply_text("❌ This bot is not authorized for this group.")
                return
            
            # Handle commands
            if update.message.text:
                text = update.message.text.strip()
                
                if text == "/start":
                    await self._handle_start(update)
                elif text == "/help":
                    await self._handle_help(update)
                elif text == "/status":
                    await self._handle_status(update)
                elif text.lower() in ['hello', 'hi', 'hey']:
                    await update.message.reply_text("👋 Hello! Please upload an MP3 file for me to process.")
            
            # Handle files
            elif update.message.document:
                document = update.message.document
                if document.mime_type and document.mime_type.startswith('audio/'):
                    await self._handle_file(update, document)
                else:
                    await update.message.reply_text("❌ Please upload an audio file (MP3, WAV, M4A).")
            
            elif update.message.audio:
                await self._handle_file(update, update.message.audio)
                
        except Exception as e:
            logger.error(f"Error handling update: {e}")
            try:
                await update.message.reply_text("❌ An error occurred. Please try again.")
            except:
                pass
    
    async def _handle_start(self, update: Update) -> None:
        """Handle /start command."""
        message = (
            "🎵 **mFAssistant - Meeting File Assistant**\n\n"
            "Upload an MP3 file and I'll create a meeting summary!\n\n"
            "**Commands:**\n"
            "• /help - Show help\n"
            "• /status - Check status\n\n"
            "Ready to process your files! 🚀"
        )
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def _handle_help(self, update: Update) -> None:
        """Handle /help command."""
        message = (
            "🔧 **mFAssistant Help**\n\n"
            "**How to use:**\n"
            "1. Upload an MP3, WAV, or M4A file\n"
            "2. Wait for processing (may take a few minutes)\n"
            "3. Receive your meeting summary!\n\n"
            f"**Max file size:** {self.settings.max_file_size_mb}MB\n"
            "**Features:** Cost-free transcription + AI analysis"
        )
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def _handle_status(self, update: Update) -> None:
        """Handle /status command."""
        message = (
            "📊 **Bot Status**\n\n"
            f"🤖 **Status:** Online ✅\n"
            f"🎵 **Whisper:** {self.settings.whisper_model_size}\n"
            f"🧠 **LLM:** {self.settings.primary_llm_model.split('/')[-1]}\n"
            f"📁 **Max Size:** {self.settings.max_file_size_mb}MB\n\n"
            "Ready to process files! 🚀"
        )
        await update.message.reply_text(message, parse_mode='Markdown')
    
    async def _handle_file(self, update: Update, file_obj) -> None:
        """Handle file upload."""
        try:
            # Check file size
            file_size_mb = file_obj.file_size / (1024 * 1024) if file_obj.file_size else 0
            
            if not self.settings.validate_file_size(file_size_mb):
                await update.message.reply_text(
                    f"❌ File too large! Max size: {self.settings.max_file_size_mb}MB"
                )
                return
            
            # Send processing message
            processing_msg = await update.message.reply_text(
                "🎵 **Processing your audio file...**\n\n"
                "📥 Starting processing...\n"
                "⏳ This may take a few minutes.\n\n"
                "I'll update you on progress!",
                parse_mode='Markdown'
            )
            
            # Create a mock context for compatibility
            class MockContext:
                def __init__(self):
                    self.error = None
            
            context = MockContext()
            
            # Process the file
            await self.message_processor.process_audio_file(
                update, context, file_obj, processing_msg
            )
            
        except Exception as e:
            logger.error(f"File processing error: {e}")
            await update.message.reply_text(
                "❌ Sorry, there was an error processing your file. Please try again."
            )
    
    def _is_authorized_group(self, update: Update) -> bool:
        """Check if message is from authorized group."""
        if not update.message or not update.message.chat:
            return False
        
        chat_id = str(update.message.chat.id)
        allowed_groups = self.settings.allowed_groups
        
        if not allowed_groups:
            logger.warning("No allowed groups specified - allowing all")
            return True
        
        return chat_id in allowed_groups
    
    async def poll_updates(self) -> None:
        """Poll for updates manually."""
        while self._running:
            try:
                # Get updates
                updates = await self.bot.get_updates(
                    offset=self._offset,
                    timeout=10,
                    allowed_updates=["message"]
                )
                
                # Process each update
                for update in updates:
                    self._offset = update.update_id + 1
                    await self.handle_update(update)
                
                # Small delay to prevent hammering
                if not updates:
                    await asyncio.sleep(1)
                    
            except Exception as e:
                logger.error(f"Error polling updates: {e}")
                await asyncio.sleep(5)  # Wait longer on error
    
    async def start(self) -> None:
        """Start the bot."""
        if not await self.setup():
            raise RuntimeError("Failed to setup bot")
        
        if not self.bot:
            raise RuntimeError("Bot not initialized")
        
        try:
            logger.info("Starting minimal Telegram bot...")
            self._running = True
            
            logger.info("Bot is running. Press Ctrl+C to stop.")
            logger.info("Using manual polling (bypasses Python 3.13 compatibility issues)")
            
            # Start polling
            await self.poll_updates()
                
        except KeyboardInterrupt:
            logger.info("Received keyboard interrupt")
        finally:
            await self.stop()
    
    async def stop(self) -> None:
        """Stop the bot."""
        self._running = False
        logger.info("Bot stopped")

# Create alias for compatibility
TelegramBot = MinimalTelegramBot
